---
import type {
    GetStoreLocatorMapDto,
    GetStoreLocatorPagesDto,
} from '@malou-io/package-dto';

import MapPage from ':components/MapPage.astro';
import StorePage from ':components/StorePage.astro';
import { config } from ':config';
import {
    PageType,
    type IStorePage,
    type PageData,
} from ':interfaces/pages.interfaces';
import { ComponentType, loadComponent } from ':utils/load-component';

// Generate static paths for each restaurant in every company
export async function getStaticPaths() {
    const resPages = await fetch(
        `${config.apiBaseUrl}/store-locator/${config.organizationId}/pages?api_key=${config.apiKey}${config.isDevMode ? `&isDevMode=${config.isDevMode}` : ''}`,
    );

    const { data: pagesData }: { data: GetStoreLocatorPagesDto } =
        await resPages.json();

    const allPages: PageData[] = pagesData.restaurantsPages.map((store) => {
        const extendedStore: IStorePage = store;
        const stores = pagesData.restaurantsPages
            .filter(({ lang }) => lang === store.lang)
            .map((s) => ({
                name: s.name,
                relativePath: s.relativePath,
            }));

        return {
            params: {
                path: store.relativePath,
            },
            props: {
                data: extendedStore,
                urls: pagesData.urls ?? {},
                stores,
                type: PageType.STORE,
            },
        };
    });

    if (pagesData.mapPages.length > 0) {
        const mapPages: PageData[] = pagesData.mapPages.map((map) => {
            const stores = pagesData.restaurantsPages
                .filter(({ lang }) => lang === map.lang)
                .map((s) => ({
                    name: s.name,
                    relativePath: s.relativePath,
                }));

            return {
                params: {
                    path: map.relativePath,
                },
                props: {
                    data: map,
                    urls: pagesData.urls ?? {},
                    stores,
                    type: PageType.MAP,
                },
            };
        });
        allPages.push(...mapPages);
    }

    return allPages;
}

// Fetch restaurant details
const { data, stores, urls, type } = Astro.props;

const [HeaderComponent, FooterComponent, MapListComponent] = await Promise.all([
    loadComponent({
        organizationName: data.organizationName,
        component: ComponentType.HEADER,
    }),
    loadComponent({
        organizationName: data.organizationName,
        component: ComponentType.FOOTER,
    }),
    loadComponent({
        organizationName: data.organizationName,
        component: ComponentType.MAP_LIST,
    }),
]);
---

{
    type === PageType.STORE && (
        <StorePage
            store={data as IStorePage}
            urls={urls}
            stores={stores}
            HeaderComponent={HeaderComponent}
            FooterComponent={FooterComponent}
        />
    )
}
{
    type === PageType.MAP && (
        <MapPage
            map={data as GetStoreLocatorMapDto}
            urls={urls}
            stores={stores}
            HeaderComponent={HeaderComponent}
            FooterComponent={FooterComponent}
            MapListComponent={MapListComponent}
        />
    )
}
