---
import type { StoreLocatorLanguage } from ':interfaces/pages.interfaces';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    lang: StoreLocatorLanguage;
}

const data = {
    fr: [
        {
            title: 'On est chez Franprix 👇',
            list: [
                {
                    title: 'PARIS 1',
                    street: '165 rue Saint-Honoré',
                    postalCode: '75001 Paris',
                    phoneNumber: '01 40 20 11 06',
                    openingHours: [
                        { days: 'Lundi – <PERSON><PERSON>', hours: '7h30 – 23h' },
                        { days: 'Dimanche', hours: '9h – 23h' },
                    ],
                },
                {
                    title: 'PARIS 4',
                    street: '19 boulevard Henri IV',
                    postalCode: '75004 Paris',
                    phoneNumber: '01 42 74 14 35',
                    openingHours: [
                        { days: '<PERSON><PERSON> – <PERSON><PERSON>', hours: '7h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 13h' },
                    ],
                },
                {
                    title: 'PARIS 6',
                    street: '106 Bd Saint-Germain',
                    postalCode: '75006 Paris',
                    phoneNumber: '01 81 70 71 22',
                    openingHours: [
                        { days: '<PERSON><PERSON> – <PERSON><PERSON>', hours: '7h20 – 22h' },
                        { days: '<PERSON>man<PERSON>', hours: '9h – 19h' },
                    ],
                },
                {
                    title: 'PARIS 8',
                    street: '20 boulevard Malesherbes',
                    postalCode: '75008 Paris',
                    phoneNumber: '', // No phone listed
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h – 21h' },
                        { days: 'Dimanche', hours: '10h – 21h' },
                    ],
                },
                {
                    title: 'PARIS 8',
                    street: '4 avenue Franklin Delano Roosevelt',
                    postalCode: '75008 Paris',
                    phoneNumber: '01 81 69 05 97',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 23h' },
                        { days: 'Dimanche', hours: '9h – 21h' },
                    ],
                },
                {
                    title: 'PARIS 11',
                    street: '63 rue de Montreuil',
                    postalCode: '75011 Paris',
                    phoneNumber: '01 43 56 80 67',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 22h' },
                        { days: 'Dimanche', hours: '9h – 21h' },
                    ],
                },
                {
                    title: 'PARIS 13',
                    street: '13-15 bis boulevard Vincent Auriol',
                    postalCode: '75013 Paris',
                    phoneNumber: '01 81 70 70 39',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 20h' },
                    ],
                },
                {
                    title: 'PARIS 14',
                    street: '121 rue du Général Leclerc',
                    postalCode: '75014 Paris',
                    phoneNumber: '01 40 44 70 47',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 00h' },
                        { days: 'Dimanche', hours: '8h30 – 00h' },
                    ],
                },
                {
                    title: 'PARIS 15',
                    street: '23 boulevard de Grenelle',
                    postalCode: '75015 Paris',
                    phoneNumber: '01 45 79 19 34',
                    openingHours: [
                        { days: 'Lundi', hours: '7h30 – 21h' },
                        { days: 'Mardi – Samedi', hours: '7h30 – 21h30' },
                        { days: 'Dimanche', hours: '9h – 20h' },
                    ],
                },
                {
                    title: 'PARIS 16',
                    street: 'Place Victor Hugo',
                    postalCode: '75016 Paris',
                    phoneNumber: '01 41 38 31 69',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 23h30' },
                        { days: 'Dimanche', hours: '8h – 21h' },
                    ],
                },
                {
                    title: 'PARIS 17',
                    street: '126 avenue de Wagram',
                    postalCode: '75017 Paris',
                    phoneNumber: '01 55 65 10 38',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 20h30' },
                        { days: 'Dimanche', hours: '8h30 – 14h' },
                    ],
                },
                {
                    title: 'PARIS 17',
                    street: '25 boulevard Gouvion St Cyr',
                    postalCode: '75017 Paris',
                    phoneNumber: '01 45 72 53 09',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 22h' },
                        { days: 'Dimanche', hours: '8h30 – 21h' },
                    ],
                },
                {
                    title: 'PARIS 18',
                    street: '76 rue des Poissonniers',
                    postalCode: '75018 Paris',
                    phoneNumber: '01 42 52 12 19',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h30 – 22h' },
                        { days: 'Dimanche', hours: '8h30 – 21h' },
                    ],
                },
                {
                    title: 'BOULOGNE-BILLANCOURT',
                    street: '20 boulevard Jean Jaurès',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '01 46 05 91 18',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h – 21h' },
                        { days: 'Dimanche', hours: '9h – 20h' },
                    ],
                },
                {
                    title: 'CRÉTEIL',
                    street: 'Centre commercial de l’Échat, 96 avenue du Général de Gaulle',
                    postalCode: '94000 Créteil',
                    phoneNumber: '01 43 39 37 21',
                    openingHours: [
                        { days: 'Lundi – Vendredi', hours: '7h – 20h15' },
                        { days: 'Samedi', hours: '9h – 20h' },
                        { days: 'Dimanche', hours: '9h – 13h30' },
                    ],
                },
                {
                    title: 'ORSAY',
                    street: '2 rue du Dr Ernest Lauriat',
                    postalCode: '91400 Orsay',
                    phoneNumber: '01 69 28 91 24',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h – 20h50' },
                        { days: 'Dimanche', hours: '9h – 19h50' },
                    ],
                },
            ],
        },
        {
            title: 'On est chez Intermarché 👇',
            list: [
                {
                    title: 'ISSY-LES-MOULINEAUX',
                    street: '100 boulevard Gallieni',
                    postalCode: '92130 Issy-les-Moulineaux',
                    phoneNumber: '01 41 33 07 80',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 19h20' },
                    ],
                },
                {
                    title: 'ISSY-LES-MOULINEAUX',
                    street: '15 cour de l’Ancienne Boulangerie',
                    postalCode: '92130 Issy-les-Moulineaux',
                    phoneNumber: '',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 19h' },
                    ],
                },
                {
                    title: 'SURESNES',
                    street: '48 avenue Jean Jaurès',
                    postalCode: '92150 Suresnes',
                    phoneNumber: '01 41 44 99 10',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 20h50' },
                    ],
                },
                {
                    title: 'BOULOGNE',
                    street: '63 rue de Sèvres',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '01 84 99 00 70',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 19h30' },
                    ],
                },
                {
                    title: 'BOULOGNE',
                    street: '87 avenue Édouard Vaillant',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '01 84 75 33 45',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '8h30 – 21h' },
                        { days: 'Dimanche', hours: '9h – 21h' },
                    ],
                },
            ],
        },
        {
            title: 'On est aussi ici 👇',
            list: [
                {
                    title: 'CARREFOUR',
                    street: 'Centre Commercial – RER La Défense',
                    postalCode: '92800 Puteaux',
                    phoneNumber: '01 84 99 00 70',
                    openingHours: [
                        { days: 'Lundi – Samedi', hours: '7h – 21h' },
                        { days: 'Dimanche', hours: '9h – 13h' },
                    ],
                },
                {
                    title: 'COSTCO',
                    street: '3 avenue de Bréhat',
                    postalCode: '91140 Villebon-sur-Yvette',
                    phoneNumber: '01 60 19 22 22',
                    openingHours: [
                        { days: 'Lundi – Vendredi', hours: '10h – 21h' },
                        { days: 'Samedi', hours: '9h – 21h' },
                        { days: 'Dimanche', hours: '10h – 21h' },
                    ],
                },
            ],
        },
    ],
    en: [
        {
            title: 'We’re in Franprix 👇',
            list: [
                {
                    title: 'PARIS 1',
                    street: '165 rue Saint-Honoré',
                    postalCode: '75001 Paris',
                    phoneNumber: '+33 1 40 20 11 06',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 11:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 11:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 4',
                    street: '19 boulevard Henri IV',
                    postalCode: '75004 Paris',
                    phoneNumber: '+33 1 42 74 14 35',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 1:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 6',
                    street: '106 Bd Saint-Germain',
                    postalCode: '75006 Paris',
                    phoneNumber: '+33 1 81 70 71 22',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:20 AM – 10:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 7:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 8',
                    street: '20 boulevard Malesherbes',
                    postalCode: '75008 Paris',
                    phoneNumber: '',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:00 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '10:00 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 8',
                    street: '4 avenue Franklin Delano Roosevelt',
                    postalCode: '75008 Paris',
                    phoneNumber: '+33 1 81 69 05 97',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 11:00 PM',
                        },
                        { days: 'Sunday', hours: '10:00 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 11',
                    street: '63 rue de Montreuil',
                    postalCode: '75011 Paris',
                    phoneNumber: '+33 1 43 56 80 67',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 10:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 13',
                    street: '13-15 bis boulevard Vincent Auriol',
                    postalCode: '75013 Paris',
                    phoneNumber: '+33 1 81 70 70 39',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 8:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 14',
                    street: '121 rue du Général Leclerc',
                    postalCode: '75014 Paris',
                    phoneNumber: '+33 1 40 44 70 47',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 12:00 AM',
                        },
                        { days: 'Sunday', hours: '8:30 AM – 12:00 AM' },
                    ],
                },
                {
                    title: 'PARIS 15',
                    street: '23 boulevard de Grenelle',
                    postalCode: '75015 Paris',
                    phoneNumber: '+33 1 45 79 19 34',
                    openingHours: [
                        { days: 'Monday', hours: '7:30 AM – 9:00 PM' },
                        {
                            days: 'Tuesday – Saturday',
                            hours: '7:30 AM – 9:30 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 8:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 16',
                    street: 'Place Victor Hugo',
                    postalCode: '75016 Paris',
                    phoneNumber: '+33 1 41 38 31 69',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 11:30 PM',
                        },
                        { days: 'Sunday', hours: '8:00 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 17',
                    street: '126 avenue de Wagram',
                    postalCode: '75017 Paris',
                    phoneNumber: '+33 1 55 65 10 38',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 8:30 PM',
                        },
                        { days: 'Sunday', hours: '8:30 AM – 2:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 17',
                    street: '25 boulevard Gouvion St Cyr',
                    postalCode: '75017 Paris',
                    phoneNumber: '+33 1 45 72 53 09',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 10:00 PM',
                        },
                        { days: 'Sunday', hours: '8:30 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'PARIS 18',
                    street: '76 rue des Poissonniers',
                    postalCode: '75018 Paris',
                    phoneNumber: '+33 1 42 52 12 19',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:30 AM – 10:00 PM',
                        },
                        { days: 'Sunday', hours: '8:30 AM – 9:00 PM' },
                    ],
                },
                {
                    title: 'BOULOGNE-BILLANCOURT',
                    street: '20 boulevard Jean Jaurès',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '+33 1 46 05 91 18',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:00 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 8:00 PM' },
                    ],
                },
                {
                    title: 'CRÉTEIL',
                    street: 'Centre commercial de l’Échat, 96 avenue du Général de Gaulle',
                    postalCode: '94000 Créteil',
                    phoneNumber: '+33 1 43 39 37 21',
                    openingHours: [
                        { days: 'Monday – Friday', hours: '7:00 AM – 8:15 PM' },
                        { days: 'Saturday', hours: '9:00 AM – 8:00 PM' },
                        { days: 'Sunday', hours: '9:00 AM – 1:30 PM' },
                    ],
                },
                {
                    title: 'ORSAY',
                    street: '2 rue du Dr Ernest Lauriat',
                    postalCode: '91400 Orsay',
                    phoneNumber: '+33 1 69 28 91 24',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:00 AM – 8:50 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 7:50 PM' },
                    ],
                },
            ],
        },
        {
            title: 'We’re in Intermarché 👇',
            list: [
                {
                    title: 'ISSY-LES-MOULINEAUX',
                    street: '100 boulevard Gallieni',
                    postalCode: '92130 Issy-les-Moulineaux',
                    phoneNumber: '+33 1 41 33 07 80',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 7:20 PM' },
                    ],
                },
                {
                    title: 'ISSY-LES-MOULINEAUX',
                    street: '15 cour de l’Ancienne Boulangerie',
                    postalCode: '92130 Issy-les-Moulineaux',
                    phoneNumber: '',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 7:00 PM' },
                    ],
                },
                {
                    title: 'SURESNES',
                    street: '48 avenue Jean Jaurès',
                    postalCode: '92150 Suresnes',
                    phoneNumber: '+33 1 41 44 99 10',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 8:50 PM' },
                    ],
                },
                {
                    title: 'BOULOGNE',
                    street: '63 rue de Sèvres',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '+33 1 84 99 00 70',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 7:30 PM' },
                    ],
                },
                {
                    title: 'BOULOGNE',
                    street: '87 avenue Édouard Vaillant',
                    postalCode: '92100 Boulogne-Billancourt',
                    phoneNumber: '+33 1 84 75 33 45',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '8:30 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 9:00 PM' },
                    ],
                },
            ],
        },
        {
            title: 'We’re here too 👇',
            list: [
                {
                    title: 'CARREFOUR',
                    street: 'Centre Commercial – RER La Défense',
                    postalCode: '92800 Puteaux',
                    phoneNumber: '+33 1 84 99 00 70',
                    openingHours: [
                        {
                            days: 'Monday – Saturday',
                            hours: '7:00 AM – 9:00 PM',
                        },
                        { days: 'Sunday', hours: '9:00 AM – 1:00 PM' },
                    ],
                },
                {
                    title: 'COSTCO',
                    street: '3 avenue de Bréhat',
                    postalCode: '91140 Villebon-sur-Yvette',
                    phoneNumber: '+33 1 60 19 22 22',
                    openingHours: [
                        {
                            days: 'Monday – Friday',
                            hours: '10:00 AM – 9:00 PM',
                        },
                        { days: 'Saturday', hours: '9:00 AM – 9:00 PM' },
                        { days: 'Sunday', hours: '10:00 AM – 9:00 PM' },
                    ],
                },
            ],
        },
    ],
    undetermined: [],
};

const { lang } = Astro.props;

const pageData = data[lang] || data['undetermined'];
---

<div class="h-10 bg-white"></div>

<div class="bg-white px-44">
    {
        pageData.map((section) => (
            <section class="pb-20">
                <h2 class="text-primary font-tertiary mb-10 text-center text-3xl">
                    {section.title}
                </h2>
                <div class="grid grid-cols-1 items-baseline justify-center gap-8 sm:grid-cols-2 lg:grid-cols-3">
                    {section.list.map((location) => (
                        <div class="mb-5 flex min-h-[270px] flex-col items-center justify-center space-y-4">
                            <p class="text-primary font-primary mb-10 text-lg text-[17px] font-semibold uppercase">
                                {location.title}
                            </p>
                            <p class="font-fourth text-center text-[17px] font-bold text-[#7a7a7a]">
                                {location.street}
                            </p>
                            <p class="font-fourth text-[17px] font-bold text-[#7a7a7a]">
                                {location.postalCode}
                            </p>

                            <p class="font-fourth min-h-[24px] text-[17px] font-bold text-[#7a7a7a]">
                                {location.phoneNumber}
                            </p>

                            <div class="flex flex-col items-center justify-center space-y-4">
                                {location.openingHours.map((hours) => (
                                    <p>
                                        <span class="text-secondary font-fourth text-[18px] font-bold">
                                            {hours.days} :
                                        </span>
                                        <span class="font-fourth text-[18px] font-bold text-[#7a7a7a]">
                                            {hours.hours}
                                        </span>
                                    </p>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </section>
        ))
    }
</div>
