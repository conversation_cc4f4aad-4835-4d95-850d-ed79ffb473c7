---
import type { StoreLocatorLanguage } from ':interfaces/pages.interfaces';
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto/src/store-locator/store-locator.response.dto';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    lang: StoreLocatorLanguage;
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
}

const bgImageUrl =
    'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/custom/images/dougnutpat.png';

const { lang, urls } = Astro.props;

const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allMenuLinks = {
    fr: [
        {
            text: 'Notre Histoire',
            href: 'https://krispykreme.fr/notre-histoire/',
        },
        {
            text: 'Programme de fidélité',
            href: 'https://krispykreme.fr/fidelite/',
        },
        {
            text: 'La carte',
            href: 'https://krispykreme.fr/menu/',
        },
        {
            text: 'Secrets de fabrication',
            href: 'https://krispykreme.fr/comment-sont-fabriques-les-doughnuts/',
        },
    ],
    en: [
        {
            text: 'Home',
            href: 'https://krispykreme.fr/en/home/',
        },
        {
            text: 'Menu',
            href: 'https://krispykreme.fr/en/doughnuts-menu-anglais/',
        },
        {
            text: 'Stores',
            href: `/${mapUrl}`,
        },
        {
            text: 'Careers',
            href: 'https://krispykreme.fr/en/careers-anglais/',
        },
        {
            text: 'Contact',
            href: 'https://krispykreme.fr/en/contact/',
        },
    ],
    undetermined: [],
};

const allHelpLinks = {
    fr: [
        {
            text: 'Nous trouver',
            href: `/${mapUrl}`,
        },
        {
            text: 'Nous rejoindre',
            href: 'https://krispykreme.fr/on-recrute/',
        },
        {
            text: 'Nous contacter',
            href: 'https://krispykreme.fr/nous-contacter/',
        },
    ],
    en: [
        {
            text: 'Rewards',
            href: 'https://krispykreme.fr/en/rewards/',
        },
        {
            text: 'Our Story',
            href: 'https://krispykreme.fr/en/our-story/',
        },
    ],
    undetermined: [],
};

const menuLinks = allMenuLinks[lang] || allMenuLinks['fr'];
const helpLinks = allHelpLinks[lang] || allHelpLinks['fr'];
---

<footer>
    <div class="font[400] font-secondary flex flex-col bg-[#005A37]">
        <div
            class="flex flex-col items-center justify-center gap-4 p-10 md:flex-row md:items-start md:gap-30"
            style={`background-image: url(${bgImageUrl}); background-repeat: repeat;`}
        >
            <div class="flex">
                <Picture
                    src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/favicons/favicon.png"
                    formats={['webp']}
                    fallbackFormat="png"
                    alt="Logo Krispy Kreme"
                    inferSize
                    width={205}
                    height={89}
                    class="mt-6 mr-4 sm:mt-0 sm:mr-0"
                    densities={[1, 2, 3]}
                />
            </div>

            <div
                class="flex flex-col gap-3 text-center text-white md:text-start"
            >
                <div class="flex flex-col gap-3 text-center md:text-start">
                    {
                        lang === 'fr' && (
                            <span class="font-primary text-[20px]">
                                Découvrir Krispy Kreme
                            </span>
                        )
                    }
                    {
                        lang === 'en' && (
                            <span class="font-primary text-[20px]">
                                MAIN
                                <span class="text-[15px] italic"> links</span>
                            </span>
                        )
                    }
                    <div class="item-center flex flex-col gap-3 text-[16px]">
                        {
                            menuLinks
                                .slice(0, 2)
                                .map((link) => (
                                    <a href={link.href}>{link.text}</a>
                                ))
                        }
                    </div>
                </div>
                <div
                    class="item-center flex flex-col gap-3 text-center md:text-start"
                >
                    {
                        lang === 'fr' && (
                            <span class="font-primary text-[20px]">
                                Nos doughnuts
                            </span>
                        )
                    }

                    <div class="item-center flex flex-col gap-3 text-[16px]">
                        {
                            menuLinks
                                .slice(2, lang === 'fr' ? 4 : 5)
                                .map((link) => (
                                    <a href={link.href}>{link.text}</a>
                                ))
                        }
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col gap-3 text-center text-white md:text-start"
            >
                {
                    lang === 'fr' && (
                        <span class="font-primary text-[20px]">
                            Aide et informations
                        </span>
                    )
                }
                {
                    lang === 'en' && (
                        <span class="font-primary text-[20px]">
                            FOR OUR
                            <span class="text-[15px] italic"> foodies</span>
                        </span>
                    )
                }

                <div class="item-center flex flex-col gap-3 text-[16px]">
                    {
                        helpLinks.map((link) => (
                            <a href={link.href}>{link.text}</a>
                        ))
                    }
                </div>
            </div>

            <div
                class="item-center flex flex-col gap-3 text-center md:text-start"
            >
                {
                    lang === 'fr' && (
                        <div class="font-primary text-[20px] text-white">
                            Rejoignez-nous
                        </div>
                    )
                }
                {
                    lang === 'en' && (
                        <span class="font-primary text-[20px] text-white">
                            JOIN IN ON THE
                            <span class="text-[15px] italic"> fun</span>
                        </span>
                    )
                }

                <div class="mb-1 flex h-[24px] gap-[5px]">
                    <div class="bg-secondary rounded-full">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.facebook.com/krispykremefrance"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 512 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary rounded-full">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.instagram.com/krispykreme.fr/"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary rounded-full">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.linkedin.com/company/krispy-kreme-france/"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary rounded-full">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://x.com/KrispyKremeFrr"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 512 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary rounded-full">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.tiktok.com/@krispykremefr"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"
                                ></path></svg
                            >
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="bg-primary item-center font-primary flex flex-wrap justify-center gap-1 p-3 text-[11px] text-white"
        >
            © KRISPY KREME FRANCE |
            <a
                href="https://krispykreme.fr/en/krispy-kreme-politique-cookies/"
                target="_blank"
            >
                POLITIQUE COOKIES
            </a>|
            <a
                href="https://krispykreme.fr/en/politique-de-confidentialite/"
                target="_blank"
            >
                POLITIQUE DE CONFIDENTIALITÉ
            </a>|
            <a
                href="https://krispykreme.fr/en/krispy-kreme-mentions-legales/"
                target="_blank"
            >
                MENTIONS LÉGALES
            </a>
        </div>
    </div>
</footer>
