---
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto';
import { Picture } from 'astro:assets';
import Cart from ':assets/icons/krispy-kreme/cart.svg';
import type { StoreLocatorLanguage } from ':interfaces/pages.interfaces';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
    lang: StoreLocatorLanguage;
    storeId: string;
}

const { urls, lang, storeId } = Astro.props as Props;

const defaultLanguage = lang;

const selectableLanguages = ['fr', 'en'];

const storeKey = `store.${storeId}`;

const languages = selectableLanguages.map((language) => {
    const url = storeId
        ? urls[language as StoreLocatorLanguage]?.[storeKey]
        : urls[language as StoreLocatorLanguage]?.map;

    return {
        key: language as StoreLocatorLanguage,
        value: language === 'fr' ? '🇫🇷 Français' : '🇬🇧 English',
        url,
    };
});

const mapUrl = urls[lang]?.map || urls['fr']?.map;

const allMenuLinks = {
    fr: [
        {
            text: 'Notre Carte',
            href: 'https://krispykreme.fr/menu/',
        },
        {
            text: 'Fidélité',
            href: 'https://krispykreme.fr/fidelite/',
        },
        {
            text: 'Savoir-faire',
            href: 'https://krispykreme.fr/comment-sont-fabriques-les-doughnuts/',
        },
        {
            text: 'Nous trouver',
            href: `/${mapUrl}`,
        },
        {
            text: 'On Recrute',
            href: 'https://krispykreme.fr/on-recrute/',
        },
        {
            text: 'Contactez-nous',
            href: 'https://krispykreme.fr/nous-contacter/',
        },
    ],
    en: [
        {
            text: 'Home',
            href: 'https://krispykreme.fr/en/home/',
        },
        {
            text: 'Menu',
            href: 'https://krispykreme.fr/en/doughnuts-menu-anglais/',
        },
        {
            text: 'Rewards',
            href: 'https://krispykreme.fr/en/rewards/',
        },
        {
            text: 'Stores',
            href: `/${mapUrl}`,
        },
        {
            text: 'Careers',
            href: 'https://krispykreme.fr/en/careers-anglais/',
        },
        {
            text: 'Contact',
            href: 'https://krispykreme.fr/en/contact/',
        },
    ],
    undetermined: [],
};

const allAboutMenuLinks = {
    fr: [
        {
            text: 'Notre Histoire',
            href: 'https://krispykreme.fr/notre-histoire/',
        },
        {
            text: 'Quelques chiffres',
            href: 'https://krispykreme.fr/quelques-chiffres/',
        },
        {
            text: 'Comment sont fabriqués les DOUGHNUTS ?',
            href: 'https://krispykreme.fr/comment-sont-fabriques-les-doughnuts/',
        },
    ],
    en: [
        {
            text: 'Sweet beginnings',
            href: 'https://krispykreme.fr/en/our-story/',
        },
        {
            text: 'International',
            href: 'https://krispykreme.fr/en/international-anglais/',
        },
    ],
    undetermined: [],
};

const menuLinks = allMenuLinks[lang];
const aboutMenuLinks = allAboutMenuLinks[lang] || allAboutMenuLinks['fr'];
---

<style>
    .krispy-kreme-header {
        box-shadow: -18px 15px 29px -30px rgba(0, 0, 0, 0.5);
    }
</style>

<div class="flex flex-col bg-white">
    <header class="font-primary top-0 left-0 z-100 h-[118px] w-full">
        <div
            class="bg-primary flex w-full justify-center pt-[8px] pb-[10px] md:pt-0 md:pb-0"
        >
            <div
                class="mx-24 flex h-full w-full max-w-[1140px] items-center justify-center pb-1.5 pl-[10px] md:mx-0 md:justify-between lg:mx-24 lg:max-w-[1245px] lg:pb-0 lg:pl-0 xl:mx-0"
            >
                <div class="mb-1 flex h-[24px] gap-[5px]">
                    <div class="bg-secondary hidden rounded-full md:block">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.facebook.com/krispykremefrance"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 512 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary hidden rounded-full md:block">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.instagram.com/krispykreme.fr/"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary hidden rounded-full md:block">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.linkedin.com/company/krispy-kreme-france/"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary hidden rounded-full md:block">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://x.com/KrispyKremeFrr"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 512 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"
                                ></path></svg
                            >
                        </a>
                    </div>
                    <div class="bg-secondary hidden rounded-full md:block">
                        <a
                            class="flex !h-6 !w-6 items-center justify-center"
                            href="https://www.tiktok.com/@krispykremefr"
                            target="_blank"
                        >
                            <svg
                                class="h-[15px] w-[15px] fill-white"
                                viewBox="0 0 448 512"
                                xmlns="http://www.w3.org/2000/svg"
                                ><path
                                    d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"
                                ></path></svg
                            >
                        </a>
                    </div>
                </div>

                <div class="font-secondary relative mr-16">
                    <div
                        id="language-selector-menu-toggle"
                        class="flex items-center gap-2 px-1 py-2 pb-3"
                    >
                        <a
                            class="text-[15px] font-normal text-white"
                            id="selected-language"
                        >
                            {
                                languages.find(
                                    (lang) => lang.key === defaultLanguage,
                                )?.value || 'Français'
                            }
                        </a>
                        <span
                            class="mb-1 h-[10px] w-[10px] hover:cursor-pointer"
                        >
                            <svg
                                id="language-selector-icon"
                                width="15"
                                height="15"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                stroke="white"
                                ><g id="SVGRepo_bgCarrier" stroke-width="0"
                                ></g><g
                                    id="SVGRepo_tracerCarrier"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"></g><g
                                    id="SVGRepo_iconCarrier"
                                >
                                    <path
                                        d="M17.9188 8.17969H11.6888H6.07877C5.11877 8.17969 4.63877 9.33969 5.31877 10.0197L10.4988 15.1997C11.3288 16.0297 12.6788 16.0297 13.5088 15.1997L15.4788 13.2297L18.6888 10.0197C19.3588 9.33969 18.8788 8.17969 17.9188 8.17969Z"
                                        fill="#ffffff"></path>
                                </g></svg
                            >
                        </span>
                    </div>

                    <ul
                        id="language-menu"
                        class="bg-primary absolute z-50 ms-auto hidden w-[100px]"
                    >
                        {
                            languages.map((language) => (
                                <li
                                    id={`${language.key}-language-item`}
                                    class="hidden pt-2 pb-1"
                                    data-key={language.key}
                                >
                                    <a
                                        href={language.url}
                                        class="pr-4 pl-1.5 text-[15px] font-normal text-white"
                                    >
                                        {language.value}
                                    </a>
                                </li>
                            ))
                        }
                    </ul>
                </div>
            </div>
        </div>

        <!-- Desktop Menu -->
        <div
            class="krispy-kreme-header hidden h-[75px] items-center font-normal xl:flex"
        >
            <div class="ml-2 w-[45%]">
                {
                    lang === 'fr' && (
                        <nav class="flex justify-end">
                            <div class="group relative">
                                <div class="flex items-center gap-3 pr-[18px]">
                                    <a class="text-primary text-[15px] uppercase transition">
                                        A propos
                                    </a>
                                    <span class="mb-1 h-[10px] w-[10px] hover:cursor-pointer">
                                        <svg
                                            class="fill-primary transition"
                                            viewBox="0 0 320 512"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
                                        </svg>
                                    </span>
                                </div>

                                <ul class="absolute left-[-10px] z-50 ms-auto hidden w-[400px] rounded-md bg-white group-hover:block">
                                    {aboutMenuLinks.map((link) => (
                                        <li>
                                            <a
                                                href={link.href}
                                                class="hover:bg-secondary text-primary block px-12 py-4.5 text-[14px] font-bold uppercase hover:text-white"
                                            >
                                                {link.text}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            {menuLinks.slice(0, 3).map((link, index) => (
                                <a
                                    href={link.href}
                                    class={`text-primary px-[15px] text-[15px] font-normal uppercase transition ${index === 2 && 'pr-6'}`}
                                >
                                    {link.text}
                                </a>
                            ))}
                        </nav>
                    )
                }{
                    lang === 'en' && (
                        <nav class="flex justify-end">
                            {menuLinks.slice(0, 3).map((link, index) => (
                                <a
                                    href={link.href}
                                    class={`text-primary pr-[50px] text-[15px] font-normal uppercase transition ${index === 2 && 'pr-8'}`}
                                >
                                    {link.text}
                                </a>
                            ))}
                            <div class="group relative">
                                <div class="flex items-center gap-3 pr-[18px]">
                                    <a class="text-primary text-[15px] uppercase transition">
                                        Our story
                                    </a>
                                    <span class="mb-1 h-[10px] w-[10px] hover:cursor-pointer">
                                        <svg
                                            class="fill-primary transition"
                                            viewBox="0 0 320 512"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
                                        </svg>
                                    </span>
                                </div>

                                <ul class="absolute left-[-10px] z-50 ms-auto hidden w-[400px] rounded-md bg-white group-hover:block">
                                    {aboutMenuLinks.map((link) => (
                                        <li>
                                            <a
                                                href={link.href}
                                                class="hover:bg-secondary text-primary block px-12 py-4.5 text-[14px] font-bold uppercase hover:text-white"
                                            >
                                                {link.text}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </nav>
                    )
                }
            </div>

            <div class="flex w-[10%] pl-1 sm:right-0">
                <div>
                    <a
                        href="https://krispykreme.fr"
                        aria-label="Page d'accueil"
                        class="block"
                    >
                        <Picture
                            src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/favicons/favicon.png"
                            formats={['webp']}
                            fallbackFormat="png"
                            alt="Logo Krispy Kreme"
                            inferSize
                            class="h-[65.7px] !w-[147px]"
                            densities={[1, 2, 3]}
                        />
                    </a>
                </div>
            </div>

            <div class="mr-4 flex w-[45%] items-center pl-6">
                <nav class="flex flex-wrap justify-start md:justify-end">
                    {
                        menuLinks.slice(3).map((link, index) => (
                            <a
                                href={link.href}
                                class={`text-primary ${index === 0 ? (lang === 'fr' ? 'pr-3' : 'pr-4') : lang === 'fr' ? 'px-3' : 'px-8'} text-[15px] font-normal uppercase transition`}
                            >
                                {link.text}
                            </a>
                        ))
                    }
                </nav>
                {
                    lang === 'fr' && (
                        <div class="relative ml-3.5 flex items-center">
                            <div class="absolute">
                                <Picture
                                    src="https://krispykreme.fr/wp-content/uploads/2025/05/Icone-new-1-e1747754986334.png"
                                    formats={['webp']}
                                    fallbackFormat="png"
                                    alt="Logo New"
                                    inferSize
                                    class="h-[45px] !w-[53px]"
                                    densities={[1, 2, 3]}
                                />
                            </div>

                            <div class="bg-secondary ml-10 px-3 py-[5px] pl-4">
                                <a href="https://krispykreme.fr/livraison-au-bureau/">
                                    <span class="flex items-center">
                                        <span class="mr-1 text-[15px] text-white uppercase transition">
                                            Entreprises
                                        </span>
                                        <Cart class="h-5 w-5" />
                                    </span>
                                </a>
                            </div>
                        </div>
                    )
                }
            </div>
        </div>

        <!-- Mobile Menu Button -->

        <div
            class="flex h-[53px] w-full items-center justify-between font-normal xl:hidden"
        >
            <div class="mt-1.5 ml-1.5 h-5 w-5 pl-10 sm:px-0">
                <button aria-label="Toggle Menu" id="mobile-menu-button">
                    <svg
                        id="menu-icon"
                        aria-hidden="true"
                        role="presentation"
                        class="fill-primary h-5.5 w-5.5"
                        viewBox="0 0 1000 1000"
                        xmlns="http://www.w3.org/2000/svg"
                        ><path
                            d="M104 333H896C929 333 958 304 958 271S929 208 896 208H104C71 208 42 237 42 271S71 333 104 333ZM104 583H896C929 583 958 554 958 521S929 458 896 458H104C71 458 42 487 42 521S71 583 104 583ZM104 833H896C929 833 958 804 958 771S929 708 896 708H104C71 708 42 737 42 771S71 833 104 833Z"
                        ></path></svg
                    >
                    <svg
                        aria-hidden="true"
                        id="close-icon"
                        role="presentation"
                        class="fill-primary hidden h-5.5 w-5.5"
                        viewBox="0 0 1000 1000"
                        xmlns="http://www.w3.org/2000/svg"
                        ><path
                            d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z"
                        ></path></svg
                    >
                </button>
            </div>

            <a
                href="https://krispykreme.fr"
                aria-label="Page d'accueil"
                class="block"
            >
                <Picture
                    src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/favicons/favicon.png"
                    formats={['webp']}
                    fallbackFormat="png"
                    alt="Logo Krispy Kreme"
                    inferSize
                    class="mt-6 mr-4 h-[65px] !w-[145px] sm:mt-0 sm:mr-0"
                    densities={[1, 2, 3]}
                />
            </a>
        </div>

        <div
            id="mobile-menu"
            class="flex max-h-0 flex-col overflow-hidden bg-white pl-5 transition-all duration-300"
        >
            {
                lang === 'fr' && (
                    <>
                        <div>
                            <div
                                id="about-menu-item"
                                class="flex items-center gap-3 px-1 py-3"
                            >
                                <a
                                    class="text-primary text-[13px] uppercase transition"
                                    id="about-menu-text"
                                >
                                    A propos
                                </a>
                                <span class="mb-1 h-[10px] w-[10px] hover:cursor-pointer">
                                    <svg
                                        id="about-menu-icon"
                                        class="fill-primary transition"
                                        viewBox="0 0 320 512"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
                                    </svg>
                                </span>
                            </div>

                            <ul
                                id="about-menu"
                                class="flex max-h-0 flex-col overflow-hidden transition-all duration-300"
                            >
                                {aboutMenuLinks.map((link) => (
                                    <li>
                                        <a
                                            href={link.href}
                                            class="hover:bg-secondary text-primary block px-5 py-[10px] text-[11px] font-medium uppercase hover:text-white"
                                        >
                                            {link.text}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {menuLinks.map((link) => (
                            <a
                                href={link.href}
                                class="text-primary py-[10px] text-[13px] font-normal uppercase transition"
                            >
                                {link.text}
                            </a>
                        ))}
                    </>
                )
            }

            {
                lang === 'en' && (
                    <>
                        {menuLinks.slice(0, 3).map((link) => (
                            <a
                                href={link.href}
                                class="text-primary py-[10px] text-[13px] font-normal uppercase transition"
                            >
                                {link.text}
                            </a>
                        ))}

                        <div>
                            <div
                                id="about-menu-item"
                                class="flex items-center gap-3 px-1 py-3"
                            >
                                <a
                                    class="text-primary text-[13px] uppercase transition"
                                    id="about-menu-text"
                                >
                                    Our Story
                                </a>
                                <span class="mb-1 h-[10px] w-[10px] hover:cursor-pointer">
                                    <svg
                                        id="about-menu-icon"
                                        class="fill-primary transition"
                                        viewBox="0 0 320 512"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
                                    </svg>
                                </span>
                            </div>

                            <ul
                                id="about-menu"
                                class="flex max-h-0 flex-col overflow-hidden transition-all duration-300"
                            >
                                {aboutMenuLinks.map((link) => (
                                    <li>
                                        <a
                                            href={link.href}
                                            class="hover:bg-secondary text-primary block px-5 py-[10px] text-[11px] font-medium uppercase hover:text-white"
                                        >
                                            {link.text}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {menuLinks.slice(4, 5).map((link) => (
                            <a
                                href={link.href}
                                class="text-primary py-[10px] text-[13px] font-normal uppercase transition"
                            >
                                {link.text}
                            </a>
                        ))}
                    </>
                )
            }

            {
                lang === 'fr' && (
                    <div class="flex items-center pt-3">
                        <div class="bg-secondary px-3 py-[5px]">
                            <a href="https://krispykreme.fr/livraison-au-bureau/">
                                <span class="mr-1 text-[15px] text-white uppercase transition">
                                    Entreprises
                                </span>
                            </a>
                        </div>

                        <Picture
                            src="https://krispykreme.fr/wp-content/uploads/2025/05/Icone-new-1-e1747754986334.png"
                            formats={['webp']}
                            fallbackFormat="png"
                            alt="Logo New"
                            inferSize
                            class="h-[30px] !w-[35px]"
                            densities={[1, 2, 3]}
                        />
                    </div>
                )
            }
        </div>

        <script
            is:inline
            define:vars={{ languages, initLanguage: defaultLanguage }}
        >
            // Mobile menu functionality
            const mobileMenuButton =
                document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const closeIcon = document.getElementById('close-icon');
            const openIcon = document.getElementById('menu-icon');

            mobileMenuButton?.addEventListener('click', (event) => {
                event.preventDefault();
                mobileMenu?.classList.toggle('max-h-0');
                mobileMenu?.classList.toggle('max-h-96');
                closeIcon?.classList.toggle('hidden');
                openIcon?.classList.toggle('hidden');
            });

            // About menu functionality
            const aboutMenuItem = document.getElementById('about-menu-item');
            const aboutMenuText = document.getElementById('about-menu-text');
            const aboutMenu = document.getElementById('about-menu');
            const aboutMenuIcon = document.getElementById('about-menu-icon');

            aboutMenuItem?.addEventListener('click', (event) => {
                event.preventDefault();
                aboutMenuItem?.classList.toggle('bg-secondary');

                aboutMenuText?.classList.toggle('text-primary');
                aboutMenuText?.classList.toggle('text-white');

                aboutMenuIcon?.classList.toggle('rotate-180');
                aboutMenuIcon?.classList.toggle('fill-white');
                aboutMenuIcon?.classList.toggle('fill-primary');

                aboutMenu?.classList.toggle('max-h-0');
                aboutMenu?.classList.toggle('max-h-96');
            });

            // Language selector functionality
            let selectedLanguage = initLanguage;

            const languageSelectorMenuToggle = document.getElementById(
                'language-selector-menu-toggle',
            );
            const selectedLanguageElement =
                document.getElementById('selected-language');
            const languageSelectorIcon = document.getElementById(
                'language-selector-icon',
            );
            const languageMenu = document.getElementById('language-menu');

            initLanguageItems();
            toggleLanguagesItems();

            languageSelectorMenuToggle?.addEventListener(
                'click',
                handleLanguageSelectorMenuToggle,
            );

            function handleLanguageSelectorMenuToggle() {
                languageSelectorIcon?.classList.toggle('rotate-180');
                languageSelectorIcon?.classList.toggle('fill-white');
                languageSelectorIcon?.classList.toggle('fill-primary');

                languageMenu?.classList.toggle('hidden');
                languageMenu?.classList.toggle('block');
            }

            function initLanguageItems() {
                languages.forEach((language) => {
                    const languageItem = document.getElementById(
                        `${language.key}-language-item`,
                    );

                    if (languageItem && selectedLanguage !== language.key) {
                        languageItem.classList.toggle('hidden');
                        languageItem.classList.toggle('block');
                    }
                });
            }

            function toggleLanguagesItems() {
                languages.forEach((language) => {
                    const languageLink = document.getElementById(
                        `${language.key}-language-item`,
                    );
                    if (languageLink) {
                        languageLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            onLanguageSelected(language.key);
                        });
                    }
                });
            }

            function onLanguageSelected(languageKey) {
                const relativePath = languages.find(
                    (lang) => lang.key === languageKey,
                )?.url;
                if (relativePath) {
                    window.location.replace(
                        window.location.origin + `/${relativePath}`,
                    );
                }
            }
        </script>
    </header>
</div>
