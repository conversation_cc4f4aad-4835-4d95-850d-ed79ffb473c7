import { DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, Renderer2, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ApplicationLanguage, HeapEventName } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { ToastService } from ':core/services/toast.service';
import { LocalStorage } from ':core/storage/local-storage';
import { StoreLocatorOrganizationsContext } from ':modules/store-locator/contexts/store-locator-organizations.context';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-marketing-page',
    imports: [TranslateModule, MatIconModule, ButtonComponent, ImagePathResolverPipe, MatButtonModule],
    templateUrl: './marketing-page.component.html',
    styleUrl: './marketing-page.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MarketingPageComponent implements OnInit {
    private readonly _storeLocatorOrganizationsContext = inject(StoreLocatorOrganizationsContext);
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _heapService = inject(HeapService);
    private readonly _renderer = inject(Renderer2);
    private readonly _document = inject(DOCUMENT);

    readonly SvgIcon = SvgIcon;

    private readonly _FRENCH_URL = 'https://www.malou.io/fonctionnalites/store-locator';
    private readonly _ENGLISH_URL = 'https://www.malou.io/en-us/features/store-locator';
    private readonly _STUDY_CASE_URL = 'https://restaurants.bioburger.fr/restaurant-burger-bio/angers-foch/';
    private readonly _VIMEO_PLAYER_SCRIPT_URL = 'https://player.vimeo.com/api/player.js';

    readonly isRequestSent = signal(false);
    readonly isSendingRequest = signal(false);

    ngOnInit(): void {
        const subRequestsSentOrganizationIds = JSON.parse(
            LocalStorage.getItem(LocalStorageKey.STORE_LOCATOR_ORGANZATIONS_SUB_REQUEST_SENT) || '[]'
        );
        this.isRequestSent.set(
            subRequestsSentOrganizationIds.includes(this._storeLocatorOrganizationsContext.selectedOrganization$.value?._id || '')
        );

        this._loadVimeoPlayerScript();
    }

    sendRequest(): void {
        const organizationId = this._storeLocatorOrganizationsContext.selectedOrganization$.value?._id;
        if (!organizationId) {
            console.error('Organization ID is not defined');
            return;
        }

        this.isSendingRequest.set(true);
        this._storeLocatorService.sendSubscriptionRequestNotification(organizationId).subscribe({
            next: () => {
                this._toastService.openSuccessToast(
                    this._translateService.instant('store_locator.marketing_page.request_successfully_sent')
                );
                this._updateStorageWithSentOrganizationId(organizationId);
                this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_MARKETING_PAGE_REQUEST_SENT, {
                    venuesIds: this._storeLocatorContext
                        .storeLocatorOrganizationRestaurants()
                        .map((restaurant) => restaurant.id)
                        .join(','),
                });
                this.isRequestSent.set(true);
                this.isSendingRequest.set(false);
            },
            error: (error) => {
                console.error('Error sending subscription request:', error);
                this.isSendingRequest.set(false);
            },
        });
    }

    redirectToMoreInformation(): void {
        const currentLang = LocalStorage.getLang();
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_MARKETING_PAGE_MORE_INFORMATION, {
            venuesIds: this._storeLocatorContext
                .storeLocatorOrganizationRestaurants()
                .map((restaurant) => restaurant.id)
                .join(','),
        });
        if (currentLang === ApplicationLanguage.FR) {
            window.open(this._FRENCH_URL, '_blank');
        } else {
            window.open(this._ENGLISH_URL, '_blank');
        }
    }

    redirectToStudyCaseStoreLocator(): void {
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_MARKETING_PAGE_STUDY_CASE);
        window.open(this._STUDY_CASE_URL, '_blank');
    }

    private _updateStorageWithSentOrganizationId(organizationId: string): void {
        const subRequestsSentOrganizationIds = JSON.parse(
            LocalStorage.getItem(LocalStorageKey.STORE_LOCATOR_ORGANZATIONS_SUB_REQUEST_SENT) || '[]'
        );
        if (!subRequestsSentOrganizationIds.includes(organizationId)) {
            subRequestsSentOrganizationIds.push(organizationId);
            LocalStorage.setItem(
                LocalStorageKey.STORE_LOCATOR_ORGANZATIONS_SUB_REQUEST_SENT,
                JSON.stringify(subRequestsSentOrganizationIds)
            );
        }
    }

    private _loadVimeoPlayerScript(): void {
        // Check if script is already loaded
        const existingScript = this._document.querySelector(`script[src="${this._VIMEO_PLAYER_SCRIPT_URL}"]`);
        if (existingScript) {
            return;
        }

        const script = this._renderer.createElement('script');
        this._renderer.setAttribute(script, 'src', this._VIMEO_PLAYER_SCRIPT_URL);
        this._renderer.setAttribute(script, 'type', 'text/javascript');
        this._renderer.setAttribute(script, 'async', 'true');

        script.onload = (): void => {
            console.log('Vimeo player script loaded successfully');
        };

        script.onerror = (): void => {
            console.error('Failed to load Vimeo player script');
        };

        this._renderer.appendChild(this._document.head, script);
    }
}
