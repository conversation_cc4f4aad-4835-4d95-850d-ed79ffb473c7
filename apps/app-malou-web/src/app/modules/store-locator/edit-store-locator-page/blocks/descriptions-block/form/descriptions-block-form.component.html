<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5">
        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-3 rounded-[10px] !bg-malou-color-background-light">
                <ng-container
                    [ngTemplateOutlet]="controlWrapperTemplate"
                    [ngTemplateOutletContext]="{
                        onClick: toggleControlPanelExpanded.bind(this, DescriptionsBlockControlType.IMAGE),
                        isPanelExpanded: getControlPanelExpanded(DescriptionsBlockControlType.IMAGE),
                        title: 'store_locator.edit_modal.controls.image_upload.name' | translate,
                        controlTemplate: imageControlTemplate,
                        isTitleControl: false,
                    }"></ng-container>

                <div class="border border-malou-color-background-dark"></div>

                <ng-container
                    [ngTemplateOutlet]="controlWrapperTemplate"
                    [ngTemplateOutletContext]="{
                        onClick: toggleControlPanelExpanded.bind(this, DescriptionsBlockControlType.TITLE),
                        isPanelExpanded: getControlPanelExpanded(DescriptionsBlockControlType.TITLE),
                        title: 'store_locator.edit_modal.controls.title.name' | translate,
                        controlTemplate: titleControlTemplate,
                        isTitleControl: true,
                    }"></ng-container>

                <div class="border border-malou-color-background-dark"></div>

                <ng-component [ngTemplateOutlet]="contentControlsTemplate" />
            </div>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" [formGroupName]="isEvenBlock() ? 'generalEven' : 'generalUneven'">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate: { order: 1 } }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="isEvenBlock() ? styleForm.get('generalEven.backgroundColor') : styleForm.get('generalUneven.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="isEvenBlock() ? styleForm.get('generalEven.titleColor') : styleForm.get('generalUneven.titleColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="isEvenBlock() ? styleForm.get('generalEven.textColor') : styleForm.get('generalUneven.textColor')" />
        </div>
    </form>
</ng-template>

<ng-template #titleControlTemplate>
    <div [formGroup]="contentForm">
        <div formGroupName="item">
            <app-store-locator-edit-page-ai-suggestion
                [generateStoreLocatorContentType]="GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION"
                [optimizeStoreLocatorContentType]="GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION"
                [isDisabled]="shouldDisableModal()"
                [forceHideMagicWand]="true"
                [control]="titleControl"
                [descriptionItemData]="{
                    type: DescriptionBlockItemControlType.TITLE,
                    itemIndex: descriptionItemIndex(),
                    controlIndex: 0,
                }">
                <app-input-text
                    class="malou-text-14--bold!"
                    formControlName="title"
                    [defaultValue]="getItem().get('title')?.value"
                    [title]="''"
                    [disabled]="false"
                    [errorMessage]="
                        getItem().get('title')?.errors && getItem().get('title')?.errors?.maxlength
                            ? ('store_locator.edit_modal.controls.title.length_error'
                              | translate
                                  : {
                                        maxLength: DescriptionsBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                    })
                            : ''
                    "
                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                    [isEmojiPickerEnabled]="false"
                    [showMaxLength]="true"
                    [maxLength]="DescriptionsBlockContentFormInputValidation.TITLE_MAX_LENGTH"
                    [placeholder]="'store_locator.edit_modal.controls.title.placeholder_text' | translate"
                    [autocapitalize]="'none'">
                </app-input-text
            ></app-store-locator-edit-page-ai-suggestion>
        </div>
    </div>
</ng-template>

<ng-template #imageControlTemplate>
    <div class="w-fit">
        @if (selectedRestaurant()) {
            <app-image-uploader
                titleClass="!malou-text-14--bold !text-malou-color-text-1"
                [media]="uploadedMedia()"
                [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                [restaurant]="selectedRestaurant()!"
                [title]="''"
                [wrapperClass]="'!p-0'"
                (mediaChange)="setMedia($event)"
                (onMediaSelected)="onMediaSelected($event)"></app-image-uploader>
        }
    </div>
</ng-template>

<ng-template #contentControlsTemplate>
    <div [formGroup]="contentForm">
        <div formGroupName="item">
            <div class="flex flex-col gap-4" formArrayName="content">
                @for (content of getDescriptionContent().controls; let index2 = $index; track index2) {
                    <div class="expansion-header malou-expansion-panel px-5">
                        <mat-accordion>
                            <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                                <mat-expansion-panel-header class="!pl-0" (click)="toggleSectionsPanelExpanded(index2)">
                                    <div class="flex w-full items-center justify-between">
                                        <div class="malou-text-13--bold text-malou-color-text-1">
                                            {{ 'store_locator.edit_modal.common.section_order' | translate: { order: index2 + 1 } }}
                                        </div>
                                        <div class="flex items-center">
                                            <mat-icon
                                                class="!w-3 transition-all"
                                                color="primary"
                                                [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                                [class.rotate-180]="getSectionsPanelExpanded(index2)"></mat-icon>
                                        </div>
                                    </div>
                                </mat-expansion-panel-header>

                                <ng-template matExpansionPanelContent>
                                    <div class="flex flex-col gap-4 !bg-malou-color-background-light" [formGroupName]="index2">
                                        <div class="flex flex-col gap-1">
                                            <app-store-locator-edit-page-ai-suggestion
                                                [title]="'store_locator.edit_modal.controls.subtitle.name' | translate"
                                                [generateStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION
                                                "
                                                [optimizeStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION
                                                "
                                                [isDisabled]="shouldDisableModal()"
                                                [control]="content.get('subtitle')"
                                                [descriptionItemData]="{
                                                    type: DescriptionBlockItemControlType.SUBTITLE,
                                                    itemIndex: descriptionItemIndex(),
                                                    controlIndex: index2,
                                                }">
                                                <app-input-text
                                                    class="malou-text-14--bold!"
                                                    formControlName="subtitle"
                                                    [theme]="InputTextTheme.EDIT_STORE_LOCATOR"
                                                    [defaultValue]="content.get('subtitle')?.value"
                                                    [placeholder]="'store_locator.edit_modal.controls.subtitle.placeholder' | translate"
                                                    [disabled]="false"
                                                    [errorMessage]="
                                                        content.get('subtitle')?.errors &&
                                                        (content.get('subtitle')?.errors?.minlength ||
                                                            content.get('subtitle')?.errors?.maxlength)
                                                            ? ('store_locator.edit_modal.controls.subtitle.length_error'
                                                              | translate
                                                                  : {
                                                                        maxLength:
                                                                            DescriptionsBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                                                    [isEmojiPickerEnabled]="false"
                                                    [showMaxLength]="true"
                                                    [maxLength]="DescriptionsBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH"
                                                    [autocapitalize]="'none'">
                                                </app-input-text
                                            ></app-store-locator-edit-page-ai-suggestion>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <app-store-locator-edit-page-ai-suggestion
                                                [title]="'store_locator.edit_modal.controls.description.name' | translate"
                                                [generateStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION
                                                "
                                                [optimizeStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION
                                                "
                                                [isDisabled]="shouldDisableModal()"
                                                [control]="content.get('text')"
                                                [descriptionItemData]="{
                                                    type: DescriptionBlockItemControlType.TEXT,
                                                    itemIndex: descriptionItemIndex(),
                                                    controlIndex: index2,
                                                }">
                                                <app-text-area
                                                    class="malou-text-14--bold!"
                                                    formControlName="text"
                                                    [defaultValue]="content.get('text')?.value"
                                                    [disabled]="false"
                                                    [theme]="InputTextAreaTheme.EDIT_STORE_LOCATOR"
                                                    [errorMessage]="
                                                        content.get('text')?.errors && content.get('text')?.errors?.maxlength
                                                            ? ('store_locator.edit_modal.controls.description.length_error'
                                                              | translate
                                                                  : {
                                                                        maxLength:
                                                                            DescriptionsBlockContentFormInputValidation.TEXT_MAX_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                                                    [rows]="5"
                                                    [isEmojiPickerEnabled]="false"
                                                    [showCounterInTitle]="false"
                                                    [maxLength]="DescriptionsBlockContentFormInputValidation.TEXT_MAX_LENGTH"
                                                    [showMaxLength]="true"
                                                    [placeholder]="'store_locator.edit_modal.controls.description.placeholder' | translate"
                                                    [autocapitalize]="'none'"></app-text-area
                                            ></app-store-locator-edit-page-ai-suggestion>
                                        </div>
                                    </div>
                                </ng-template>
                            </mat-expansion-panel>
                        </mat-accordion>
                    </div>
                    @if (index2 !== getDescriptionContent().controls.length - 1) {
                        <div class="border border-malou-color-background-dark"></div>
                    }
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template
    let-onClick="onClick"
    let-title="title"
    let-controlTemplate="controlTemplate"
    let-isPanelExpanded="isPanelExpanded"
    let-isTitleControl="isTitleControl"
    #controlWrapperTemplate>
    <div class="expansion-header malou-expansion-panel px-5">
        <mat-accordion>
            <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                <mat-expansion-panel-header class="!pl-0" (click)="void 0">
                    <div class="flex w-full items-center justify-between">
                        <div class="malou-text-13--bold text-malou-color-text-1">
                            {{ title }}
                        </div>

                        <div class="flex items-center">
                            @if (isTitleControl) {
                                <div class="mr-4">
                                    <app-store-locator-edit-page-ai-suggestion
                                        [generateStoreLocatorContentType]="
                                            GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION
                                        "
                                        [optimizeStoreLocatorContentType]="
                                            GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION
                                        "
                                        [isDisabled]="shouldDisableModal()"
                                        [forceHideButtons]="true"
                                        [control]="titleControl"
                                        [descriptionItemData]="{
                                            type: DescriptionBlockItemControlType.TITLE,
                                            itemIndex: descriptionItemIndex(),
                                            controlIndex: 0,
                                        }"></app-store-locator-edit-page-ai-suggestion>
                                </div>
                            }
                            <mat-icon
                                class="!w-3 transition-all"
                                color="primary"
                                [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                [class.rotate-180]="isPanelExpanded"
                                (click)="onClick()"></mat-icon>
                        </div>
                    </div>
                </mat-expansion-panel-header>

                <ng-template matExpansionPanelContent>
                    <div class="flex flex-col gap-2 !bg-malou-color-background-light">
                        <ng-container [ngTemplateOutlet]="controlTemplate"></ng-container>
                    </div>
                </ng-template>
            </mat-expansion-panel>
        </mat-accordion>
    </div>
</ng-template>
