import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { combineLatest, skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import {
    DescriptionBlockSection,
    DescriptionsBlockContentFormInputValidation,
    DescriptionsBlockControlType,
    DescriptionsBlockSectionForm,
    DescriptionsBlockStyleData,
    DescriptionsBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/descriptions-block/descriptions-block.interface';
import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageAiSuggestionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.component';
import { DescriptionBlockItemControlType } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.interface';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { Media } from ':shared/models';

@Component({
    selector: 'app-store-locator-edit-page-descriptions-block-form',
    templateUrl: './descriptions-block-form.component.html',
    styleUrls: ['./descriptions-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
        TextAreaComponent,
        ImageUploaderComponent,
        NgTemplateOutlet,
        EditStoreLocatorPageAiSuggestionComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageDescriptionBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    // To specify which item to select of the descriptions block in the store locator.
    readonly descriptionItemIndex = input<number>(0);

    private readonly _formBuilder = inject(FormBuilder);

    readonly DescriptionBlockItemControlType = DescriptionBlockItemControlType;
    readonly DescriptionsBlockControlType = DescriptionsBlockControlType;
    readonly DescriptionsBlockContentFormInputValidation = DescriptionsBlockContentFormInputValidation;

    contentForm: FormGroup<DescriptionsBlockSectionForm>;
    styleForm: FormGroup<DescriptionsBlockStyleForm>;

    readonly isControlPanelExpanded: WritableSignal<Record<DescriptionsBlockControlType, boolean>> = signal({
        [DescriptionsBlockControlType.TITLE]: false,
        [DescriptionsBlockControlType.IMAGE]: false,
    });

    readonly sectionsPanelExpanded: WritableSignal<boolean[]> = signal([]);

    readonly uploadedMedia: WritableSignal<Media | null> = signal<Media | null>(null);

    readonly descriptionsBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.DESCRIPTION)?.()?.data
    );

    readonly isEvenBlock = computed(() => this.descriptionItemIndex() % 2 === 0);

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
        combineLatest([toObservable(this.currentEditingRestaurant), toObservable(this.selectedRestaurantPagesLanguage)])
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        toObservable(this.descriptionItemIndex)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.controls.item.get('title') as AbstractControl | null;
    }

    getItem(): DescriptionsBlockSectionForm['item'] {
        return this.contentForm.controls.item as DescriptionsBlockSectionForm['item'];
    }

    getDescriptionContent(): FormArray {
        return this.contentForm.controls.item.get('content') as FormArray;
    }

    onMediaSelected(media: Media | null): void {
        const imageUrlControl = this.contentForm.controls.item.get('imageUrl');
        if (media) {
            imageUrlControl?.setValue(media.getMediaUrl());
        }
    }

    setMedia(media: Media | null): void {
        if (media) {
            this.uploadedMedia.set(media);
            this._updateMedia();
        }
    }

    getControlPanelExpanded(controlType: DescriptionsBlockControlType): boolean {
        return this.isControlPanelExpanded()?.[controlType] ?? false;
    }

    toggleControlPanelExpanded(controlType: DescriptionsBlockControlType): void {
        this.isControlPanelExpanded.set({ ...this.isControlPanelExpanded(), [controlType]: !this.isControlPanelExpanded()[controlType] });
    }

    getSectionsPanelExpanded(index: number): boolean {
        return this.sectionsPanelExpanded()[index] ?? false;
    }

    toggleSectionsPanelExpanded(index: number): void {
        const currentState = this.sectionsPanelExpanded();
        currentState[index] = !currentState[index];
        this.sectionsPanelExpanded.set([...currentState]);
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.contentForm.controls.item.get('title');
        const contentControls = this.contentForm.controls.item.get('content') as FormArray;
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isContentInError = contentControls.controls.some((contentControl) => {
            const subtitleControl = contentControl.get('subtitle');
            const textControl = contentControl.get('text');
            return (!!subtitleControl?.errors && subtitleControl.dirty) || (!!textControl?.errors && textControl.dirty);
        });
        const isItemInError = isTitleInError || isContentInError;
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.DESCRIPTION,
            isError: isItemInError,
        });
    }

    private _initContentForm(): void {
        const descriptionBlockData = this.descriptionsBlockData();

        if (!this.contentForm) {
            const descriptionBlockItem = descriptionBlockData?.items[this.descriptionItemIndex()] ?? {
                title: '',
                image: { url: '', description: '', mediaId: undefined },
                blocks: [{ title: '', text: '' }],
            };
            this.contentForm = this._formBuilder.group({
                item: this._createDescriptionSectionGroupForm({
                    title: descriptionBlockItem.title,
                    imageUrl: descriptionBlockItem.image.url,
                    content: descriptionBlockItem.blocks.map((contentData) => ({
                        subtitle: contentData.title,
                        text: contentData.text,
                    })),
                }),
            });

            this.uploadedMedia.set(
                new Media({
                    urls: { original: descriptionBlockItem.image.url || '' },
                    dimensions: {},
                })
            );

            this.contentForm.valueChanges.subscribe((value: { item: DescriptionBlockSection }) => {
                const blockData = this.descriptionsBlockData();
                this._checkIfBlockInError();
                if (blockData && blockData?.items.length !== 0) {
                    this._trackEditContentChanges(value.item);
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.DESCRIPTION,
                        blockData: {
                            items: blockData.items.map((item, index) => {
                                if (index !== this.descriptionItemIndex()) {
                                    return item;
                                }
                                return {
                                    image: {
                                        url: this.uploadedMedia()?.urls?.original ?? item.image.url,
                                        description: blockData.items[index].image.description,
                                    },
                                    title: value.item?.title ?? item.title,
                                    blocks: item.blocks.map((content, subIndex) => ({
                                        title: value.item?.content[subIndex].subtitle ?? content.title,
                                        text: value.item?.content[subIndex].text ?? content.text,
                                    })),
                                };
                            }),
                        },
                    });
                }
            });
        }
    }

    private _createDescriptionSectionGroupForm(data: DescriptionBlockSection): FormGroup {
        return this._formBuilder.group({
            title: this._formBuilder.control(data.title, {
                validators: [Validators.required, Validators.maxLength(DescriptionsBlockContentFormInputValidation.TITLE_MAX_LENGTH)],
                nonNullable: true,
            }),
            content: this._formBuilder.array(data.content.map((contentData) => this._createDescriptionContentGroupForm(contentData))),
        });
    }

    private _createDescriptionContentGroupForm(data: DescriptionBlockSection['content'][number]): FormGroup {
        this.sectionsPanelExpanded().push(false); // Initialize the section panel state for each content item
        return this._formBuilder.group({
            subtitle: this._formBuilder.control(data.subtitle, {
                validators: [Validators.required, Validators.maxLength(DescriptionsBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH)],
                nonNullable: true,
            }),
            text: this._formBuilder.control(data.text, {
                validators: [Validators.required, Validators.maxLength(DescriptionsBlockContentFormInputValidation.TEXT_MAX_LENGTH)],
                nonNullable: true,
            }),
        });
    }

    private _patchContentForm(): void {
        const descriptionBlockData = this.descriptionsBlockData();
        if (!descriptionBlockData) {
            return;
        }
        this.uploadedMedia.set(
            new Media({
                urls: { original: descriptionBlockData.items[this.descriptionItemIndex()]?.image.url || '' },
                dimensions: {},
            })
        );
        this.contentForm.patchValue({
            item: {
                title: descriptionBlockData.items[this.descriptionItemIndex()]?.title || '',
                imageUrl: descriptionBlockData.items[this.descriptionItemIndex()]?.image.url || '',
                content:
                    descriptionBlockData.items[this.descriptionItemIndex()]?.blocks.map((block) => ({
                        subtitle: block.title,
                        text: block.text,
                    })) || [],
            },
        });
        this.contentForm.markAsPristine();
    }

    private _updateMedia(): void {
        const descriptionsBlockData = this.descriptionsBlockData();
        const uploadedMedia = this.uploadedMedia();
        if (descriptionsBlockData && uploadedMedia) {
            this.storeLocatorPageState()?.updateBlock({
                blockType: StoreLocatorPageBlockType.DESCRIPTION,
                blockData: {
                    items: descriptionsBlockData.items.map((item, index) => {
                        if (index !== this.descriptionItemIndex()) {
                            return item;
                        }
                        return {
                            ...item,
                            image: {
                                url: uploadedMedia.urls.original,
                                description: item.image.description,
                                mediaId: uploadedMedia.id,
                            },
                        };
                    }),
                },
            });
        }
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN,
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN,
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN,
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN,
        ]);

        const titleBlockEven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN] || {};
        const descriptionBlockEven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN] || {};
        const titleBlockUneven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN] || {};
        const descriptionBlockUneven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN] || {};

        this.styleForm = this._formBuilder.group({
            generalEven: this._formBuilder.group({
                textColor: this._formBuilder.control(descriptionBlockEven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(descriptionBlockEven.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                titleColor: this._formBuilder.control(titleBlockEven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            generalUneven: this._formBuilder.group({
                textColor: this._formBuilder.control(descriptionBlockUneven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(descriptionBlockUneven.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                titleColor: this._formBuilder.control(titleBlockUneven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });

        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as DescriptionsBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: DescriptionsBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN,
                    data: [
                        {
                            value: value.generalEven.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN,
                    data: [
                        {
                            value: value.generalEven.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.generalEven.textColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN,
                    data: [
                        {
                            value: value.generalUneven.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN,
                    data: [
                        {
                            value: value.generalUneven.textColor,
                            propertyType: PropertyType.Color,
                        },
                        {
                            value: value.generalUneven.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
            ]);
            this.trackEditStyleActivity({
                block: StoreLocatorPageBlockType.DESCRIPTION,
                changes: dataToUpdate,
            });
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }

    private _trackEditContentChanges(changes: DescriptionBlockSection): void {
        const descriptionBlockData = this.descriptionsBlockData();
        if (descriptionBlockData) {
            const descriptionBlockItem = descriptionBlockData.items[this.descriptionItemIndex()];
            if (descriptionBlockItem.title !== changes.title) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.DESCRIPTION,
                    element: StoreLocatorInputType.TITLE,
                });
            }

            if (descriptionBlockItem.image.url !== changes.imageUrl) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.DESCRIPTION,
                    element: StoreLocatorInputType.PHOTO,
                });
            }

            if (
                this.isElementTrackedForRestaurant(StoreLocatorInputType.SUBTITLE, StoreLocatorPageBlockType.DESCRIPTION) &&
                this.isElementTrackedForRestaurant(StoreLocatorInputType.TEXT, StoreLocatorPageBlockType.DESCRIPTION)
            ) {
                return;
            }
            changes.content.forEach((content, index) => {
                const currentContent = descriptionBlockItem.blocks[index];
                if (currentContent) {
                    if (currentContent.title !== content.subtitle) {
                        this.trackEditContentActivity({
                            block: StoreLocatorPageBlockType.DESCRIPTION,
                            element: StoreLocatorInputType.SUBTITLE,
                        });
                    }
                    if (currentContent.text !== content.text) {
                        this.trackEditContentActivity({
                            block: StoreLocatorPageBlockType.DESCRIPTION,
                            element: StoreLocatorInputType.TEXT,
                        });
                    }
                }
            });
        }
    }
}
