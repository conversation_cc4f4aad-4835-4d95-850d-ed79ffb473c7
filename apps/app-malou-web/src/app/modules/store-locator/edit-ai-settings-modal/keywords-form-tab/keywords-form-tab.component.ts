import { TitleCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { uniqBy } from 'lodash';

import { mapLanguageStringToApplicationLanguage, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { LocalStorage } from ':core/storage/local-storage';
import { SpecialAttributeForm } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationKeyword } from ':modules/store-locator/models/store-locator-organization-keyword';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

type AiSettingsAttribute = StoreLocatorOrganizationConfiguration['aiSettings']['attributes'][number];
type AiSettingsKeyword = StoreLocatorOrganizationConfiguration['aiSettings']['keywords'][number];

@Component({
    selector: 'app-keywords-form-tab',
    imports: [
        FormsModule,
        ReactiveFormsModule,
        MatCheckboxModule,
        ApplyPurePipe,
        TranslateModule,
        TitleCasePipe,
        MatIconModule,
        MatTooltipModule,
    ],
    templateUrl: './keywords-form-tab.component.html',
    styleUrl: './keywords-form-tab.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KeywordsFormTabComponent {
    readonly storeLocatorAiSettings = input.required<StoreLocatorOrganizationConfiguration['aiSettings']>();
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            attributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();
    readonly organizationRestaurants = input.required<StoreLocatorOrganizationRestaurant[]>();
    readonly organizationRestaurantKeywords = input.required<StoreLocatorOrganizationKeyword[]>();
    readonly currentLang = mapLanguageStringToApplicationLanguage(LocalStorage.getLang());

    private readonly _MAX_ATTRIBUTES_COUNT = 10;
    private readonly _MAX_KEYWORDS_COUNT = 10;

    readonly SvgIcon = SvgIcon;
    readonly allKeywords = computed<AiSettingsKeyword[]>(() => {
        const organizationKeywords = this._getKeywordsToDisplay(this.organizationRestaurantKeywords());
        const allkeywords = [...organizationKeywords, ...this.storeLocatorAiSettings().keywords];
        return uniqBy(allkeywords, 'restaurantKeywordId');
    });

    readonly allAttributes = computed<AiSettingsAttribute[]>(() => {
        const attributes = this._getRestaurantsAttributesToDisplay(this.organizationRestaurants());
        const allAttributes = [...attributes, ...this.storeLocatorAiSettings().attributes];
        return uniqBy(allAttributes, 'attributeId');
    });

    onAttributeToggle(attributeId: string, isChecked: boolean): void {
        const ids = this.aiSettingsForm().controls.attributeIds.value || [];
        const updatedIds = isChecked ? [...ids, attributeId] : ids.filter((id) => id !== attributeId);
        this.aiSettingsForm().controls.attributeIds.setValue(updatedIds);
    }

    onKeywordToggle(restaurantKeywordId: string, isChecked: boolean): void {
        const ids = this.aiSettingsForm().controls.restaurantKeywordIds.value || [];
        const updatedIds = isChecked ? [...ids, restaurantKeywordId] : ids.filter((id) => id !== restaurantKeywordId);
        this.aiSettingsForm().controls.restaurantKeywordIds.setValue(updatedIds);
    }

    isAttributeChecked = (attributeId: string): boolean => {
        const attributeIds = this.aiSettingsForm().controls.attributeIds.value || [];
        return attributeIds.includes(attributeId);
    };

    isKeywordChecked = (restaurantKeywordId: string): boolean => {
        const restaurantKeywordIds = this.aiSettingsForm().controls.restaurantKeywordIds.value || [];
        return restaurantKeywordIds.includes(restaurantKeywordId);
    };

    private _getRestaurantsAttributesToDisplay(organizationRestaurants: StoreLocatorOrganizationRestaurant[]): AiSettingsAttribute[] {
        return StoreLocatorOrganizationRestaurant.getSelectableAttributes(organizationRestaurants, this._MAX_ATTRIBUTES_COUNT);
    }

    private _getKeywordsToDisplay(organizationRestaurantKeywords: StoreLocatorOrganizationKeyword[]): AiSettingsKeyword[] {
        return StoreLocatorOrganizationKeyword.getSelectableKeywords(organizationRestaurantKeywords, this._MAX_KEYWORDS_COUNT);
    }
}
