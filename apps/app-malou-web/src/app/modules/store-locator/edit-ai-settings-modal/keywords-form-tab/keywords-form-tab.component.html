<div class="flex w-full flex-col gap-6">
    <div class="flex flex-col gap-3">
        <div class="malou-text-14--bold flex items-center gap-x-1 text-malou-color-text-1">
            {{ 'store_locator.edit_ai_settings_modal.keywords_form.characteristics' | translate }}
            <mat-icon
                class="!h-4 !w-4"
                color="text-malou-color-text-1"
                [matTooltip]="'store_locator.edit_ai_settings_modal.keywords_form.characteristics_info' | translate"
                [svgIcon]="SvgIcon.INFO_ROUND"></mat-icon>
        </div>
        <div class="grid grid-cols-3 gap-2">
            @for (attribute of allAttributes(); track attribute.attributeId) {
                <div class="flex items-center gap-x-2">
                    <mat-checkbox
                        color="primary"
                        [checked]="isAttributeChecked | applyPure: attribute.attributeId"
                        (change)="onAttributeToggle(attribute.attributeId, $event.checked)">
                        <span class="malou-text-12--medium text-malou-color-text-1">
                            {{ attribute.attributeName[currentLang] }}
                        </span>
                    </mat-checkbox>
                </div>
            }
        </div>
    </div>

    <div class="flex flex-col gap-3">
        <div class="malou-text-14--bold text-malou-color-text-1">
            {{ 'store_locator.edit_ai_settings_modal.keywords_form.keywords' | translate }}
        </div>
        <div class="grid grid-cols-3 gap-2">
            @for (keyword of allKeywords(); track keyword.restaurantKeywordId) {
                <div class="flex items-center gap-x-2">
                    <mat-checkbox
                        color="primary"
                        [checked]="isKeywordChecked | applyPure: keyword.restaurantKeywordId"
                        (change)="onKeywordToggle(keyword.restaurantKeywordId, $event.checked)">
                        <span class="malou-text-12--medium text-malou-color-text-1">
                            {{ keyword.text | titlecase }}
                        </span>
                    </mat-checkbox>
                </div>
            }
        </div>
    </div>
</div>
