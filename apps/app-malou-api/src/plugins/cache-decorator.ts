import objectHash from 'object-hash';
import { container } from 'tsyringe';

import { InjectionToken } from ':helpers/injection';

import { Cache } from './cache';

type CacheOptions = {
    ttlInSeconds: number;
    computeCacheKeyArgs?: (...args: any[]) => any[];
};

/**
 * Decorator to cache the result of a class method.
 */
export function cacheDecorator(options: CacheOptions) {
    const cache = container.resolve<Cache>(InjectionToken.Cache);

    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const className = target.constructor.name;

        descriptor.value = async function (...args: any[]) {
            // Generate cache key
            const cacheKeyArgs = options.computeCacheKeyArgs ? options.computeCacheKeyArgs(...args) : args;
            const cacheKey = generateCacheKey(className, propertyKey, cacheKeyArgs);

            // Try to get cached result
            const cachedResult = await cache.get(cacheKey);
            if (cachedResult !== null) {
                return JSON.parse(cachedResult);
            }

            // Call original method if cache miss
            const result = await originalMethod.apply(this, args);

            // Cache the result
            await cache.set(cacheKey, JSON.stringify(result), options.ttlInSeconds);

            return result;
        };

        return descriptor;
    };
}

function generateCacheKey(className: string, methodName: string, args: any[]): string {
    const argsHash = objectHash(args);
    const keyString = `${className}:${methodName}:${argsHash}`;
    return keyString;
}
