import CryptoJS from 'crypto-js';
import fs from 'fs';
import LanguageDetect from 'languagedetect';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import { omit } from 'lodash';
import fetch from 'node-fetch';
import crypto from 'node:crypto';
import { PassThrough, Readable } from 'node:stream';
import Path from 'path';
import { v4 } from 'uuid';
import z from 'zod';

import {
    COUNTRIES,
    COUNTRY_CODES,
    errorReplacer,
    FileFormat,
    getDeliveryPlatformKeys,
    getPlatformDefinition,
    Langs,
    CountryCode as MalouCountryCode,
    MalouErrorCode,
    MediaType,
    PlatformKey,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { AWSSESEmailTemplateName } from ':helpers/enums/AWS-SES-email-template.enum';
import { logger } from ':helpers/logger';
import { BooleanString } from ':helpers/types/boolean-string';
import { getGeoCoding<PERSON>orAddress, getGeoCodingForPlaceId } from ':plugins/gmaps';

type FacebookErrorType = 'GraphMethodException' | 'OAuthException'; // we only deal with this for the moment

export enum FacebookErrorCode {
    OAUTH = 10,
    GRAPH_METHOD = 100,
}

type RawFacebookError = {
    error: {
        message: string;
        type: FacebookErrorType;
        code: FacebookErrorCode;
        error_subcode?: number;
        fbtrace_id: string;
    };
};

type ParsedFacebookError = RawFacebookError['error'];

export const TEN_MINUTES_IN_SECONDS = 600;

export const sum = (accumulator, currentValue) => accumulator + currentValue;

/**
 * Check type variable. This method allows type 'array' and handle cases with type object.
 * Example: in JS typeof [] = 'object', with this method, typeCheck([], 'object') => false.
 * @param {any} variable
 * @param {string} type 'boolean' | 'number' | 'array' | 'object' | 'null' | 'undefined' | 'string'
 * @returns boolean
 */
export const typeCheck = (variable: any, type: string) => {
    if (type === 'array') return Array.isArray(variable);
    if (type === 'null') return variable === null;
    if (Array.isArray(variable) && type === 'object') return false;
    if (variable === null && type === 'object') return false;
    // eslint-disable-next-line valid-typeof
    return typeof variable === type;
};

/**
 * This method tries to get result several times until it gets a result without errorMessage (use for lambdas)
 * If it is impossible will return an object with error
 */
export const retryPromisePlatformsScrapper = async (promise: Promise<any>, tries: number, waitTime: number) => {
    try {
        for (let index = 1; index <= tries; index += 1) {
            const res = await promise;
            if (!res.errorMessage) {
                return res;
            }
            if (!res.errorMessage.match(/spotted/)) {
                return { error: true, message: MalouErrorCode.PLATFORM_SCRAPPER_ERROR, errorData: res };
            }
            if (index === tries) {
                return { error: true, message: MalouErrorCode.SCRAPPER_TOO_MANY_TRIES };
            }
            await waitFor(waitTime * Math.random());
        }
    } catch (e) {
        if (JSON.stringify(e, errorReplacer).match(/Endpoint request timed out/) && tries > 0) {
            logger.warn('[SCRAPPER_TIMED_OUT]');
            retryPromisePlatformsScrapper(promise, tries - 1, waitTime);
        } else {
            throw new MalouError(MalouErrorCode.HELPERS_RETRY_PLATFORM_SCRAPPER_ERROR, {
                message: 'There was an unexpected error trying promises',
                metadata: {
                    rawError: e,
                    error: JSON.stringify(e, errorReplacer),
                },
            });
        }
    }
};

/**
 * Tries to find the language of the text. If it is included in accepted langs, returns the lang object else undefined
 */
export const getAcceptedLang = (text: string): { full: string; short: string } | undefined => {
    const lngDetector = new LanguageDetect();
    const detectedLangs = lngDetector.detect(text);
    if (detectedLangs.length === 0) {
        return;
    }
    const detectedLang = detectedLangs[0][0];
    const languages: { full: string; short: string }[] = Object.values(Langs);
    if (languages.map((l: any) => l.full).includes(detectedLang)) {
        return languages.find((l: { full: string; short: string }) => l.full === detectedLang);
    }
};

export const getCountryCodeFromCountryNameFr = (frenchCountryName: string) => {
    if (!frenchCountryName) {
        return null;
    }
    const entry = COUNTRIES.find((c) => c.name_fr.toLowerCase() === frenchCountryName.trim().toLowerCase());
    return entry?.code || null;
};

export const getCountryNameFrFromCountryCode = (countryCode: string) => {
    if (!countryCode) {
        return null;
    }
    const entry = COUNTRIES.find((c) => c.code.toLowerCase() === countryCode.trim().toLowerCase());
    return entry?.name_fr || null;
};

export const getCountryDialCodeFromCountryNameFr = (countryName: string) => {
    if (!countryName) {
        return '';
    }
    const entry = COUNTRIES.find((c) => c.name_fr.toLowerCase() === countryName.trim().toLowerCase());
    return entry?.dial_code || '';
};

export const encryptPassword = (password: string, secret?: string) =>
    CryptoJS.AES.encrypt(JSON.stringify(password), secret || String(Math.random())).toString();

export const decryptPassword = (password: string, secret?: string) => {
    const bytes = CryptoJS.AES.decrypt(password, secret || String(Math.random()));
    return bytes.toString(CryptoJS.enc.Utf8)?.replaceAll('"', '');
};

/**
 *
 * @param {Object} restaurantProperties properties from GMB api
 * Takes restaurantProperties from GMB. If there is no latlng property, calls gmaps api to get it
 * from placeId, otherwise returns actual lat and lng
 */
export const getGeolocationForRestaurant = async (unknownParams: unknown): Promise<{ latitude: number; longitude: number }> => {
    logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] parameters', unknownParams);
    const {
        metadata: { placeId },
        latlng,
        storefrontAddress,
    } = z
        .object({
            metadata: z.object({
                placeId: z.string().nullish(),
            }),
            latlng: z.object({ latitude: z.number(), longitude: z.number() }).nullish(),
            storefrontAddress: z
                .object({
                    addressLines: z.array(z.string()).nullish(),
                    languageCode: z.string().nullish(),
                    locality: z.string().nullish(),
                    postalCode: z.string().nullish(),
                    regionCode: z.string().nullish(),
                })
                .describe(
                    'https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#Location.PostalAddress'
                )
                .nullish(),
        })
        .describe('https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#resource:-location')
        .parse(unknownParams);

    if (latlng) {
        logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] geo coordinates in parameters');
        return { latitude: latlng.latitude, longitude: latlng.longitude };
    }

    logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] geocoding from place ID', { placeId });
    const response = await getGeoCodingForPlaceId(placeId ?? null);
    logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] geocoding from place ID result ', response.data);
    if (response?.data?.error_message?.match(/The provided Place ID is no longer valid/)) {
        // this often happens with brand new restaurants that appear on GMB but don’t have
        // a valid place ID yet
        // https://airtable.com/appIqBldyX7wZlWnp/tblbOxMTpexQyxSTV/viwVSdtBlz857nQiA/recEfZ9a4UDcXD4bK?blocks=hide
        logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] geocoding from placeId failed');
        if (storefrontAddress?.addressLines?.length) {
            const address: string = [
                storefrontAddress.addressLines.join(' '),
                storefrontAddress.postalCode,
                storefrontAddress.locality,
                storefrontAddress.regionCode,
            ]
                .filter((c) => c)
                .join(' ');
            logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] trying to geocode from address', { address });
            const addrResponse = await getGeoCodingForAddress(address);
            if (!addrResponse?.data?.results?.length) {
                throw new MalouError(MalouErrorCode.GMB_PLACE_ID_IS_NO_LONGER_VALID, { metadata: { placeId } });
            }
            logger.info('[GET_GEOLOCATION_FOR_RESTAURANT] geocoding from address result', addrResponse.data);
            const place = addrResponse.data.results[0];
            return { latitude: place.geometry.location.lat, longitude: place.geometry.location.lng };
        }
        throw new MalouError(MalouErrorCode.GMB_PLACE_ID_IS_NO_LONGER_VALID, { metadata: { placeId } });
    }

    const place = response.data.results[0];
    return {
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng,
    };
};

export const isPlatformOauthConnected = (platformKey: string): boolean => {
    return getPlatformDefinition(platformKey)?.oauth ?? false;
};

export const randomString = (length: number) =>
    // eslint-disable-next-line no-mixed-operators
    Math.round(36 ** (length + 1) - Math.random() * 36 ** length)
        .toString(36)
        .slice(1);

const deg2rad = (degree: number) => degree * (Math.PI / 180);

export const getDistanceFromLatLonInKm = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const earthRadiusInKm = 6371;
    const dLat = deg2rad(lat2 - lat1); // deg2rad below
    const dLon = deg2rad(lon2 - lon1);
    const a =
        // eslint-disable-next-line no-mixed-operators
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        // eslint-disable-next-line no-mixed-operators
        Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return earthRadiusInKm * c; // Distance in km
};

export const mapToMalouPhone = (
    phone: string,
    country: MalouCountryCode = MalouCountryCode.FRANCE
): { prefix: number; digits: number } | null => {
    const phoneString = phone.toString();
    const countryCode = COUNTRY_CODES.find((c) => c === country.toUpperCase()) as CountryCode;
    try {
        const nationalNumber = parsePhoneNumber(phoneString, countryCode).nationalNumber;
        const parsedPhone = parsePhoneNumber(nationalNumber, countryCode);
        return {
            prefix: parseInt(parsedPhone.countryCallingCode, 10),
            digits: parseInt(parsedPhone.nationalNumber, 10),
        };
    } catch (err) {
        return null;
    }
};

export const capitalizeFirstLetters = (string: string): string | null => {
    if (!string) {
        return null;
    }
    const stringWords = string.split(' ');
    const capitalizedWords = stringWords.map((word) => word.charAt(0).toUpperCase() + word.slice(1));
    return capitalizedWords.join(' ');
};

export const shouldUpsertWithLinkedComponents = (rawPlatformData: { sentToSQS: boolean }, platformKey: PlatformKey) =>
    !(rawPlatformData.sentToSQS || [PlatformKey.ZENCHEF].includes(platformKey));

export const isHashtagList = (text: string) => !!text?.trim().match(/^(#[A-Za-zÀ-ÿ\d]*(\s)?)*$/);

export const downloadFile = async (url: string, rootFolder: string): Promise<{ extension: string; path: string }> => {
    const response = await fetch(url, {
        headers: {
            'User-Agent':
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15',
        },
    });
    const extension = `${response.headers.raw()['content-type'][0].split('/')[1]}`;
    const path = Path.resolve(`./${rootFolder}/${v4()}.${extension}`);
    if (fs.existsSync(`./${rootFolder}/.keep`)) {
        const writer = fs.createWriteStream(path);
        const read: any = await new Promise((resolve) => {
            response.body.pipe(writer).on('close', () => {
                resolve(fs.createReadStream(path));
            });
        });
        return { extension, path: read.path };
    }
    throw new MalouError(MalouErrorCode.HELPERS_DOWNLOAD_FILE_MISSING_FOLDER, { message: 'Missing downloadedMedias folder' });
};

export const downloadFiles = async (urls: string[], rootFolder: string) =>
    Promise.all([urls].flat().map((url) => downloadFile(url, rootFolder)));

export const extractMalouFormatFromStringUrl = (src: string): MediaType.PHOTO | MediaType.VIDEO | null => {
    const lastPointOccIndex = src.lastIndexOf('.');
    const extension = src.substring(lastPointOccIndex + 1).toLocaleLowerCase();
    switch (extension) {
        case FileFormat.JPEG:
        case FileFormat.JPG:
        case FileFormat.PNG:
            return MediaType.PHOTO;
        case FileFormat.MP4:
        case FileFormat.MOV:
        case FileFormat.QUICKTIME:
            return MediaType.VIDEO;
        default:
            return null;
    }
};

export const extractMalouFormatFromStringUrlRemoveQueryParameters = (src: string): MediaType.PHOTO | MediaType.VIDEO | null => {
    const srcWithoutQueryParams = src.split('?')[0];
    const lastPointOccIndex = srcWithoutQueryParams.lastIndexOf('.');
    const extension = srcWithoutQueryParams.substring(lastPointOccIndex + 1).toLocaleLowerCase();
    switch (extension) {
        case FileFormat.JPEG:
        case FileFormat.JPG:
        case FileFormat.PNG:
            return MediaType.PHOTO;
        case FileFormat.MP4:
        case FileFormat.MOV:
        case FileFormat.QUICKTIME:
            return MediaType.VIDEO;
        default:
            return null;
    }
};

// https://stackoverflow.com/questions/29292921/how-to-use-promise-all-with-an-object-as-input
export const promisedProperties = async (object: Object) => {
    const promisedPropertiesArr: any[] = [];
    const objectKeys = Object.keys(object);

    objectKeys.forEach((key) => promisedPropertiesArr.push(object[key]));

    return Promise.all(promisedPropertiesArr).then((resolvedValues) =>
        resolvedValues.reduce((resolvedObject, property, index) => {
            resolvedObject[objectKeys[index]] = property;
            return resolvedObject;
        }, object)
    );
};

export const getContentTypeByFile = (fileName: string): string => {
    let rc = 'application/octet-stream';
    const fn = fileName.toLowerCase();

    if (fn.indexOf('.html') >= 0) rc = 'text/html';
    else if (fn.indexOf('.css') >= 0) rc = 'text/css';
    else if (fn.indexOf('.json') >= 0) rc = 'application/json';
    else if (fn.indexOf('.js') >= 0) rc = 'application/x-javascript';
    else if (fn.indexOf('.png') >= 0) rc = 'image/png';
    else if (fn.indexOf('.jpg') >= 0) rc = 'image/jpg';
    else if (fn.indexOf('.jpeg') >= 0) rc = 'image/jpeg';

    return rc;
};

// eslint-disable-next-line arrow-body-style
export const removeNullOrUndefinedField = <T extends Object = Object>(obj: T): Partial<T> => {
    return Object.entries(obj).reduce((a, [k, v]) => (v === null || v === undefined ? a : { ...a, [k]: v }), {});
};

export const getTypeFromMimetype = (mimetype: string): MediaType => {
    switch (mimetype) {
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/png':
        case 'application/octet-stream':
            return MediaType.PHOTO;
        case 'video/mp4':
        case 'video/quicktime':
        case 'video/mov':
            return MediaType.VIDEO;
        default:
            return MediaType.FILE;
    }
};

export const getTypeFromExtension = (extension: string): MediaType => {
    switch (extension.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
        case 'png':
            return MediaType.PHOTO;
        case 'mp4':
        case 'mov':
        case 'quicktime':
            return MediaType.VIDEO;
        default:
            return MediaType.FILE;
    }
};

export const getMimetypeFromExtension = (extension: string): string => {
    switch (extension.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
            return 'image/jpeg';
        case 'png':
            return 'image/png';
        case 'mp4':
            return 'video/mp4';
        case 'mov':
            return 'video/quicktime';
        default:
            return 'application/octet-stream';
    }
};

export const isRejected = (v: any): v is PromiseRejectedResult => v?.status === 'rejected';

export const isFulfilled = (v: any): v is PromiseFulfilledResult<any> => v?.status === 'fulfilled';

export const getDateRange = (endDate: Date = new Date(), daysDiff: number = 0): { start: string; end: string } => {
    const temp = new Date(endDate);
    const startDate = new Date(temp.setDate(temp.getDate() - Math.abs(daysDiff)));
    return {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0],
    };
};

export const sliceArrayIntoChunks = <T = any>(arr: Array<T>, chunkSize: number): Array<Array<T>> => {
    if (chunkSize <= 0) {
        return [arr];
    }
    const res: T[][] = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
        const chunk = arr.slice(i, i + chunkSize);
        res.push(chunk);
    }
    return res;
};

export const findCharacterIndices = (str: string, char: string): number[] => {
    const indices: number[] = [];
    const charLowerCased = char.toLowerCase();
    for (let i = 0; i < str.length; i++) {
        if (str.charAt(i).toLowerCase() === charLowerCased) {
            indices.push(i);
        }
    }
    return indices;
};

export const appendCharToString = (string: string, char: string | null): string => {
    if (!char) {
        return string;
    }
    string += char;
    return string;
};

export const getCharAfterSubstring = (string: string, substring: string): string | null => {
    if (!string.includes(substring)) {
        return null;
    }
    const nextCharIndex = substring.length + string.indexOf(substring);
    if (nextCharIndex < string.length) {
        return string[nextCharIndex];
    }
    return null;
};

/**
 * Converts a string to a regex string that matches the string with or without diacritics.
 * For example, 'é' will match 'é', 'e', 'è', 'é', 'ê', 'ë', etc.
 * @param str The string to convert in lower case.
 */
export function toDiacriticInsensitiveRegexString(str: string): string {
    return str
        .replace(/[aáàäâ]/g, '[aáàäâ]')
        .replace(/[eéëè]/g, '[eéëè]')
        .replace(/[iíïì]/g, '[iíïì]')
        .replace(/[uüúù]/g, '[uüúù]')
        .replace(/[oóöô]/g, '[oóöô]')
        .replace(/[cç]/g, '[cç]')
        .replace(/[nñ]/g, '[nñ]');
}

export const transformToArray = <T>(value: T | Array<T>): Array<T> => {
    if (!Array.isArray(value)) {
        return [value];
    }
    return value;
};

export function getAWSSESEmailTemplateName(templateName: AWSSESEmailTemplateName | string): string {
    return `${templateName}_${process.env.NODE_ENV}`;
}

export function isDeliveryPlatform(platformKey: PlatformKey): boolean {
    return getDeliveryPlatformKeys().includes(platformKey);
}

export function booleanStringToBoolean(value: BooleanString): boolean {
    return value === 'true';
}

export const digestMessage = async (message: string, salt: string): Promise<string> => {
    const saltedMessage = message + salt;
    const msgUint8 = new TextEncoder().encode(saltedMessage);
    const hashBuffer = await crypto.webcrypto.subtle.digest('SHA-256', msgUint8);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
};

export const encodeString = (message: string): string => Buffer.from(message, 'utf8').toString('base64');
export const decodeString = (message: string): string => Buffer.from(message, 'base64').toString('utf8');

export const isFacebookError = (err: ParsedFacebookError | Error): err is ParsedFacebookError => {
    if ('code' in err && 'fbtrace_id' in err) {
        return true;
    }
    return false;
};

export const getMalouCountryCode = (countryCode: string): MalouCountryCode | undefined => {
    return COUNTRY_CODES.find((c) => c === countryCode);
};

export const transformArrayToObject = (array: any[], key: string) => {
    return array.reduce((acc, curr) => {
        acc[curr[key]] = omit(curr, key);
        return acc;
    }, {});
};

const chainPrefix = (prefix: Buffer, suffix: Readable): Readable => {
    const s = new PassThrough();
    if (prefix.length) {
        s.push(prefix);
    }
    suffix.pipe(s);
    return s;
};

/**
 * Returns the `n` first bytes emitted by a readable stream, along with a new readable
 * stream which is equivalent to the given input stream. Can return a Buffer smaller than
 * `n` if the stream emits less than `n` bytes.
 *
 * This function consumes the given readable stream and the caller must not attempt to
 * read data from it.
 */
export const getStreamBegin = (readable: Readable, n: number): Promise<[Buffer, Readable]> =>
    new Promise((resolve, reject) => {
        let buffer = Buffer.from([]);
        const end = () => {
            resolve([buffer.subarray(0, n), chainPrefix(buffer, readable)]);
        };
        readable.on('end', end);

        const onChunk = (chunk) => {
            buffer = Buffer.concat([buffer, chunk]);
            if (buffer.length >= n) {
                readable.off('end', end);
                readable.off('data', onChunk);
                end();
            }
        };

        readable.on('data', onChunk);

        readable.on('error', reject);
    });
