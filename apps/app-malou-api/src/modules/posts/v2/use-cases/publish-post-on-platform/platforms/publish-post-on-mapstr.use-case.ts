import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { MediaType, PostPublicationStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import { Platform } from ':modules/platforms/platforms.entity';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { PublishPostOnPlatform } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-platform.interface';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { MapMapstrCreatePostErrorToPublicationError } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/map-mapstr-create-post-error-to-publication-error.service';
import { PublishPostOnMapstrService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-post-on-mapstr.service';

export enum MapstrPostType {
    PHOTO = 'PHOTO',
}

@singleton()
export class PublishPostOnMapstrUseCase implements PublishPostOnPlatform {
    constructor(
        private readonly _publishPostOnMapstrService: PublishPostOnMapstrService,
        private readonly _postsRepository: PostsRepository,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _mapMapstrCreatePostErrorToPublicationError: MapMapstrCreatePostErrorToPublicationError,
        private readonly _formatMediaService: FormatMediaService
    ) {}

    async execute(post: IPopulatedPost, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[PublishPostOnMapstrUseCase] Start', {
            postId: post._id.toString(),
            platformId: platform._id.toString(),
            credentialId,
        });

        const mapstrPostKind = this._getMapstrPostType(post);
        logger.info('[PublishPostOnMapstrUseCase] mapstrPostKind computed', { instagramPostKind: mapstrPostKind });

        if (mapstrPostKind !== MapstrPostType.PHOTO) {
            logger.error('[PublishPostOnMapstrUseCase] MapstrPostKind not handled', { mapstrPostKind });
            throw new Error(`MapstrPostKind not handled ${mapstrPostKind}`);
        }

        const mediaStoredObjects = await this._formatMediaService.formatMedias({ medias: [post.attachments[0]], isReel: false });
        const mediaStoredObject = mediaStoredObjects[0];

        const res = await this._publishPostOnMapstrService.execute(post, platform, credentialId, mediaStoredObject.storedObject.publicUrl);

        if (res.isErr()) {
            logger.error('[PublishPostOnMapstrUseCase] Error', { error: res.error });
            const postId = post._id.toString();
            await this._postsRepository.updatePublicationStatus(postId, PostPublicationStatus.ERROR);
            const publicationErrorCode = this._mapMapstrCreatePostErrorToPublicationError.execute(res.error.code);
            const publicationError = { happenedAt: new Date(), data: JSON.stringify(res.error), code: publicationErrorCode };
            await this._postsRepository.pushPublicationError(postId, publicationError);
            await this._createPostErrorNotificationProducer.execute({ postId });
            return;
        }

        await this._postsRepository.updatePublicationStatus(post._id.toString(), PostPublicationStatus.PUBLISHED);

        logger.info('[PublishPostOnMapstrUseCase] End');
    }

    private _getMapstrPostType = (post: IPopulatedPost): MapstrPostType | null => {
        if (post.attachments.length === 1 && post.attachments[0].type === MediaType.PHOTO) {
            return MapstrPostType.PHOTO;
        }
        return null;
    };
}
