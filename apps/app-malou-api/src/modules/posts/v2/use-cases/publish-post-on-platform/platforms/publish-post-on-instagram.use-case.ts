import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost, IPost, toDbId } from '@malou-io/package-models';
import { FacebookApiMediaType, MediaType, PostPublicationStatus, PostType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    GetIgMediaResponse,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { PublishPostOnPlatform } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-platform.interface';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleMetaPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-meta-publication-error.service';
import { PublishCarouselOnInstagramService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-carousel-on-instagram.service';
import { PublishPhotoOrReelOnInstagramService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-photo-or-reel-on-instagram.service';
import { SlackChannel, SlackService } from ':services/slack.service';

export enum InstagramPostType {
    PHOTO = 'PHOTO',
    CAROUSEL = 'CAROUSEL',
    REEL = 'REEL',
}

@singleton()
export class PublishPostOnInstagramUseCase implements PublishPostOnPlatform {
    constructor(
        private readonly _publishPhotoOrReelOnInstagramService: PublishPhotoOrReelOnInstagramService,
        private readonly _publishCarouselOnInstagramService: PublishCarouselOnInstagramService,
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _postsRepository: PostsRepository,
        private readonly _slackService: SlackService,
        private readonly _handleMetaPublicationErrorService: HandleMetaPublicationErrorService,
        private readonly _formatMediaService: FormatMediaService,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute(post: IPopulatedPost, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[PublishPostOnInstagramUseCase] Start', {
            postId: post._id.toString(),
            platformId: platform._id.toString(),
            credentialId,
        });

        const instagramPostKind = this._getInstagramPostType(post);
        logger.info('[PublishPostOnInstagramUseCase] instagramPostKind computed', { instagramPostKind });

        let res: Result<{ mediaId: string }, MetaGraphApiHelperErrorObject>;

        if (instagramPostKind === InstagramPostType.PHOTO) {
            const mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                isReel: false,
            });
            res = await this._publishPhotoOrReelOnInstagramService.execute(post, platform, credentialId, {
                type: 'photo',
                photoUrl: mediaStoredObjects[0].storedObject.publicUrl,
            });
        } else if (instagramPostKind === InstagramPostType.CAROUSEL) {
            const mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                isReel: false,
            });
            const mediaUrls = mediaStoredObjects.map((m) => ({ type: m.type, url: m.storedObject.publicUrl }));
            res = await this._publishCarouselOnInstagramService.execute(post, platform, credentialId, mediaUrls);
        } else if (instagramPostKind === InstagramPostType.REEL) {
            const mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                isReel: true,
            });
            const thumbnailUrl = await this.getThumbnailUrl(post);
            res = await this._publishPhotoOrReelOnInstagramService.execute(post, platform, credentialId, {
                type: 'reel',
                videoUrl: mediaStoredObjects[0].storedObject.publicUrl,
                thumbnailUrl,
            });
        } else {
            logger.error('[PublishPostOnInstagramUseCase] InstagramPostKind not handled', { instagramPostKind });
            throw new Error(`InstagramPostKind not handled ${instagramPostKind}`);
        }

        if (res.isErr()) {
            logger.error('[PublishPostOnInstagramUseCase] Error', { error: res.error });
            await this._handleMetaPublicationErrorService.execute(res.error, post._id.toString());
            return;
        }

        const igUserId = platform.socialId;
        assert(igUserId, 'Missing socialId on platform');
        assert(post.platformId, 'Post does not have a platformId');
        const updateRes = await this._updatePostFromIgApi(
            post.platformId.toString(),
            post._id.toString(),
            credentialId,
            igUserId,
            res.value.mediaId
        );
        if (updateRes.isErr()) {
            logger.error('[PublishPostOnInstagramUseCase] Error when updating post', {
                postId: post._id.toString(),
                error: updateRes.error,
            });
            const line1 = `INSTAGRAM PostId: ${post._id.toString()}`;
            const line2 = `ErrorCode: ${updateRes.error.code}  Endpoint: ${updateRes.error.endpoint}`;
            const line3 = `\`\`\`${updateRes.error.stringifiedRawError}\`\`\``;
            const text = `${line1}\n${line2}\n${line3}`;
            this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
            logger.warn('[PublishPostOnInstagramUseCase] Post not updated');
        } else {
            logger.info('[PublishPostOnInstagramUseCase] Post updated');
        }
    }

    private _getInstagramPostType = (post: IPopulatedPost): InstagramPostType | null => {
        if (post.isStory) {
            return null;
        }
        if (post.postType === PostType.REEL) {
            return InstagramPostType.REEL;
        }
        if (post.attachments.length === 1 && post.attachments[0].type === MediaType.PHOTO) {
            return InstagramPostType.PHOTO;
        }
        if (post.attachments.length > 1 && post.attachments.length <= 10) {
            return InstagramPostType.CAROUSEL;
        }
        return null;
    };

    private async getThumbnailUrl(post: IPopulatedPost): Promise<string | undefined> {
        const areNewThumbnailsFieldsPresent = post.reelThumbnailFromMedia || post.reelThumbnailFromFrame;
        if (areNewThumbnailsFieldsPresent) {
            const mediaId = post.reelThumbnailFromMedia ?? post.reelThumbnailFromFrame?.media;
            if (!mediaId) {
                return undefined;
            }
            const media = await this._mediasRepository.findById(mediaId.toString());
            return media?.storedObjects?.normalized?.publicUrl ?? media?.urls?.igFit ?? media?.urls?.original;
        } else {
            return post.thumbnail?.storedObjects?.normalized?.publicUrl ?? post.thumbnail?.urls?.igFit ?? post.thumbnail?.urls?.original;
        }
    }

    private async _updatePostFromIgApi(
        platformId: string,
        postId: string,
        credentialId: string,
        igUserId: string,
        igMediaId: string
    ): Promise<Result<void, MetaGraphApiHelperErrorObject>> {
        const igMediaRes = await this._metaGraphApiHelper.getIgMedia(credentialId, igUserId, igMediaId);
        if (igMediaRes.isErr()) {
            return err(igMediaRes.error);
        }
        const igMedia = igMediaRes.value;
        const mappedIgMediaToMalouPost: Partial<IPost> = {
            published: PostPublicationStatus.PUBLISHED,
            socialId: igMedia.id,
            socialCreatedAt: new Date(igMedia.timestamp),
            socialUpdatedAt: new Date(igMedia.timestamp),
            socialLink: igMedia.permalink,
            text: igMedia.caption,
            socialAttachments: this._getSocialAttachments(igMedia),
            isPublishing: false,
        };

        await this._postsRepository.deleteOne({
            filter: { platformId: toDbId(platformId), socialId: igMedia.id },
        });
        await this._postsRepository.updateOne({ filter: { _id: toDbId(postId) }, update: mappedIgMediaToMalouPost });

        return ok(undefined);
    }

    private _getSocialAttachments(igMedia: GetIgMediaResponse): IPost['socialAttachments'] {
        const socialAttachments: IPost['socialAttachments'] = [];

        if (igMedia.media_product_type === 'FEED' && igMedia.media_type === FacebookApiMediaType.IMAGE) {
            socialAttachments.push({
                type: SocialAttachmentsMediaTypes.IMAGE,
                urls: {
                    original: igMedia.media_url,
                },
                thumbnailUrl: igMedia.media_url,
            });
        }

        if (igMedia.media_product_type === 'FEED' && igMedia.media_type === FacebookApiMediaType.CAROUSEL_ALBUM) {
            igMedia.children.data.forEach((child) => {
                if (child.media_type === FacebookApiMediaType.IMAGE) {
                    socialAttachments.push({
                        type: SocialAttachmentsMediaTypes.IMAGE,
                        urls: {
                            original: child.media_url,
                        },
                        thumbnailUrl: child.media_url,
                    });
                }
                if (child.media_type === FacebookApiMediaType.VIDEO) {
                    socialAttachments.push({
                        type: SocialAttachmentsMediaTypes.VIDEO,
                        urls: {
                            original: child.media_url,
                        },
                        thumbnailUrl: child.thumbnail_url,
                    });
                }
            });
        }

        if (igMedia.media_product_type === 'REELS' && igMedia.media_type === FacebookApiMediaType.VIDEO) {
            socialAttachments.push({
                type: SocialAttachmentsMediaTypes.VIDEO,
                urls: {
                    original: igMedia.media_url,
                },
                thumbnailUrl: igMedia.thumbnail_url,
            });
        }

        return socialAttachments;
    }
}
