import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { PlatformKey, PostType, TiktokPostPublishFailedReason, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import PostsRepository from ':modules/posts/posts.repository';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleTiktokPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-tiktok-publication-error.service';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';
import {
    FileUploadMethod,
    PhotosPostMethod,
    PublishPhotosInitRequestBody,
    PublishVideoInitRequestBody,
} from ':providers/tiktok/validators';

/**
 * After initializing the publication of a Tiktok post, we receive updated information about the post via Webhook.
 * See HandleTiktokIncomingEventsUseCase for more information.
 */
@singleton()
export class TiktokPostInitPublishUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider,
        private readonly _handleTiktokPublicationErrorService: HandleTiktokPublicationErrorService,
        private readonly _formatMediaService: FormatMediaService
    ) {}

    async execute({ post }: { post: IPopulatedPost }): Promise<void> {
        try {
            let publishId: string;

            if (post.postType === PostType.REEL) {
                const tiktokPublishVideoInitRequestBody = await this._mapMalouPostToTiktokVideoPost({ post });
                const {
                    data: { publish_id },
                } = await this._callTiktokApiService.execute({
                    restaurantId: post.restaurantId.toString(),
                    method: this._tiktokProvider.initVideoPublication,
                    args: {
                        body: tiktokPublishVideoInitRequestBody,
                    },
                });
                publishId = publish_id;
            } else {
                const tiktokPublishCarouselInitRequestBody = await this._mapMalouPostToTiktokCarouselPost({ post });
                const {
                    data: { publish_id },
                } = await this._callTiktokApiService.execute({
                    restaurantId: post.restaurantId.toString(),
                    method: this._tiktokProvider.initPostPublication,
                    args: {
                        body: tiktokPublishCarouselInitRequestBody,
                    },
                });
                publishId = publish_id;
            }
            await this._postsRepository.updateOne({
                filter: { _id: post._id },
                update: { tiktokPublishId: publishId },
            });
        } catch (err: any) {
            logger.error('[TIKTOK API] Error while initializing video publication', {
                err,
            });
            const tiktokError = err?.response?.data?.error?.code as TiktokPostPublishFailedReason | undefined;
            if (!tiktokError) {
                throw err;
            }
            await this._handleTiktokPublicationErrorService.execute(tiktokError, post._id.toString());
        }
    }

    private async _mapMalouPostToTiktokVideoPost({ post }: { post: IPopulatedPost }): Promise<PublishVideoInitRequestBody> {
        const selectedHashtagsText = post.hashtags?.selected?.map((hashtag) => hashtag.text).join(' ') ?? '';
        const textWithHashtags = selectedHashtagsText ? `${post.text}\n\n${selectedHashtagsText}` : post.text;

        const mediaStoredObjects = await this._formatMediaService.formatMedias({ medias: [post.attachments[0]], isReel: true });
        const mediaUrl = mediaStoredObjects.map((m) => ({ type: m.type, url: m.storedObject.publicUrl }))[0];

        return {
            post_info: {
                video_cover_timestamp_ms: Math.round(
                    post.reelThumbnailFromFrame?.thumbnailOffsetTimeInMs ?? post.thumbnailOffsetTimeInMs ?? 0
                ), // must be an int
                brand_content_toggle: post.tiktokOptions?.contentDisclosureSettings?.brandedContent ?? false,
                brand_organic_toggle: post.tiktokOptions?.contentDisclosureSettings?.yourBrand ?? false,
                disable_comment: post.tiktokOptions?.interactionAbility?.comment === false,
                disable_duet: post.tiktokOptions?.interactionAbility?.duet === false,
                disable_stitch: post.tiktokOptions?.interactionAbility?.stitch === false,
                is_aigc: false,
                privacy_level: post.tiktokOptions?.privacyStatus ?? TiktokPrivacyStatus.SELF_ONLY,
                title: textWithHashtags,
            },
            source_info: {
                source: FileUploadMethod.PULL_FROM_URL,
                video_url: mediaUrl.url,
            },
        };
    }

    private async _mapMalouPostToTiktokCarouselPost({ post }: { post: IPopulatedPost }): Promise<PublishPhotosInitRequestBody> {
        const selectedHashtagsText = post.hashtags?.selected?.map((hashtag) => hashtag.text).join(' ') ?? '';
        const textWithHashtags = selectedHashtagsText ? `${post.text}\n\n${selectedHashtagsText}` : post.text;

        const mediaStoredObjects = await this._formatMediaService.formatMedias(
            { medias: post.attachments, isReel: false },
            PlatformKey.TIKTOK
        );
        const mediaUrls = mediaStoredObjects.map((m) => ({ type: m.type, url: m.storedObject.publicUrl }));

        return {
            media_type: 'PHOTO',
            post_mode: PhotosPostMethod.DIRECT_POST,
            post_info: {
                brand_content_toggle: post.tiktokOptions?.contentDisclosureSettings?.brandedContent,
                brand_organic_toggle: post.tiktokOptions?.contentDisclosureSettings?.yourBrand,
                disable_comment: post.tiktokOptions?.interactionAbility?.comment === false,
                privacy_level: post.tiktokOptions?.privacyStatus ?? TiktokPrivacyStatus.SELF_ONLY,
                description: textWithHashtags,
                title: post.title,
                auto_add_music: post.tiktokOptions?.autoAddMusic ?? false,
            },
            source_info: {
                source: FileUploadMethod.PULL_FROM_URL,
                photo_cover_index: 0,
                photo_images: mediaUrls.map((m) => m.url),
            },
        };
    }
}
