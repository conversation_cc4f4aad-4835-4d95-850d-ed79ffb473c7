import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import PostsRepository from ':modules/posts/posts.repository';
import { HandleTiktokPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-tiktok-publication-error.service';
import {
    postPublishFailedEventContentValidator,
    TiktokEventRequestBody,
} from ':modules/webhooks/platforms/tiktok/validators/webhook-events.validators';

@singleton()
export class TiktokHandlePostPublishFailedUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _handleTiktokPublicationErrorService: HandleTiktokPublicationErrorService
    ) {}

    // Used when post publish has failed. Triggered on post.publish.failed webhook event
    async execute({ body }: { body: TiktokEventRequestBody }): Promise<void> {
        const { content } = body;
        const { publish_id: publishId, reason } = await postPublishFailedEventContentValidator.parseAsync(JSON.parse(content));

        const post = await this._postsRepository.findOne({ filter: { tiktokPublishId: publishId } });
        if (!post) {
            logger.error('[TIKTOK API] [TiktokHandlePostPublishFailedUseCase] Post not found', { publishId });
            return;
        }
        const postId = post._id.toString();
        logger.error('[TIKTOK API] [TiktokHandlePostPublishFailedUseCase] Post publish failed', { publishId, reason, postId });

        await this._handleTiktokPublicationErrorService.execute(reason, postId);
    }
}
