import { singleton } from 'tsyringe';

import { PostPublicationStatus } from '@malou-io/package-utils';

import PostsRepository from ':modules/posts/posts.repository';
import {
    postPublishCompleteEventContentValidator,
    TiktokEventRequestBody,
} from ':modules/webhooks/platforms/tiktok/validators/webhook-events.validators';

@singleton()
export class TiktokHandlePostPublishCompletedUseCase {
    constructor(private readonly _postsRepository: PostsRepository) {}

    // Used when post has been published. This event is triggered no matter if the post has been published publicly or not.
    // Triggered on post.publish.complete webhook event
    async execute({ body }: { body: TiktokEventRequestBody }): Promise<void> {
        const { content } = body;
        const { publish_id: publishId } = await postPublishCompleteEventContentValidator.parseAsync(JSON.parse(content));

        // Get post and mark as published
        await this._postsRepository.updateOne({
            filter: { tiktokPublishId: publishId },
            update: { published: PostPublicationStatus.PUBLISHED, isPublishing: false },
        });
    }
}
