export interface TripAdvisorRawReview {
    id: number;
    title: string;
    text: string;
    publishedDate: string;
    rating: number;
    originalLanguage: string;
    language: string;
    mgmtResponse?: {
        id: string;
        publishedDate: string;
        text: string;
        username: string;
    };
    userProfile?: {
        displayName: string;
    };
    username: string;
    photos: TripAdvisorPhotoResults;
}

type TripAdvisorPhotoResult = {
    photo: {
        __typename: 'Media_PhotoResult';
        id: number;
        photoSizeDynamic: {
            urlTemplate: string;
            maxWidth: number;
            maxHeight: number;
        };
        caption: string;
        albumId: number;
        statuses: string[];
    };
};

type TripAdvisorPhotoResults = TripAdvisorPhotoResult[];

export interface TripAdvisorRawReviewDeterminedSorting extends TripAdvisorRawReview {
    reviewDetailPageWrapper: {
        reviewDetailPageRoute: {
            url: string;
        };
    };
}

export interface TripAdvisorRawReviewDefaultSorting extends TripAdvisorRawReview {
    url: string;
}
export interface TripAdvisorApiResponseServerDeterminedSorting {
    data?: {
        ReviewsProxy_getReviewListPageForLocation?: Array<{
            reviews: TripAdvisorRawReviewDeterminedSorting[];
        }>;
    };
}

export interface TripAdvisorApiResponseDefaultSorting {
    data: {
        locations: Array<{
            reviewListPage: {
                reviews: TripAdvisorRawReviewDefaultSorting[];
            };
        }>;
    };
}

export interface TripAdvisorApiResponseOriginalTexts {
    data: {
        ReviewsProxy_getBestLanguageMatchReviewById: Array<{
            reviews: {
                title: string;
                text: string;
            };
        }>;
    };
}
