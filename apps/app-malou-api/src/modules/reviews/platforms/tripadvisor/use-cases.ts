import { sampleSize } from 'lodash';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform, IReview } from '@malou-io/package-models';
import { isFulfilled, MalouErrorCode, PlatformKey, PostedStatus, TimeInSeconds } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import { fetchUntilWorks } from ':microservices/node-crawler';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformHeaderConfigOptions, replyToReview } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import {
    TripAdvisorApiResponseDefaultSorting,
    TripAdvisorApiResponseOriginalTexts,
    TripAdvisorApiResponseServerDeterminedSorting,
    TripAdvisorRawReview,
    TripAdvisorRawReviewDefaultSorting,
    TripAdvisorRawReviewDeterminedSorting,
} from ':modules/reviews/platforms/tripadvisor/interfaces';
import {
    extractBusinessId,
    TripadvisorReply,
    TripadvisorReplyPayload,
    TripadvisorReview,
} from ':modules/reviews/platforms/tripadvisor/tripadvisor-review-mapper';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { Cache } from ':plugins/cache';
import { SlackChannel, SlackService } from ':services/slack.service';

export const MAX_TRIPADVISOR_REPLY_RETRIES = 20;

@autoInjectable()
export default class TripadvisorReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        @inject(SlackService) private readonly _slackService: SlackService,
        @inject(InjectionToken.Cache) private readonly _cache: Cache,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async getReviewsData({ restaurantId }: { restaurantId?: string }, recentOnly?: boolean): Promise<TripadvisorReview[]> {
        assert(restaurantId, 'Missing restaurantId');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TRIPADVISOR);
        const socialId = platform?.socialId;
        assert(socialId, 'Missing socialId');

        const DEFAULT_REVIEW_LIMIT = 20;
        const RECENT_ONLY_REVIEW_COUNT = 60;
        const FULL_REVIEW_COUNT = 1500;
        const maxOffset = recentOnly ? RECENT_ONLY_REVIEW_COUNT : FULL_REVIEW_COUNT;
        const reviewsPerPage = DEFAULT_REVIEW_LIMIT;

        const promises: Promise<TripadvisorReview[]>[] = [];

        for (let offset = 0; offset <= maxOffset; offset += reviewsPerPage) {
            promises.push(this._fetchReviewsWithFallback(socialId, offset, reviewsPerPage));
        }

        const allResults = await Promise.allSettled(promises);
        const validResults = allResults.filter(isFulfilled).map((r) => r.value);
        const failedResults = allResults.filter(isRejected).map((r) => r.reason);

        if (validResults.length === 0) {
            logger.warn('[FAILED_TRIP_REVIEWS] No valid results there is an issue with the fetch', { failedResults, socialId });
            reviewsFetchCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
            throw new Error(failedResults?.[0]?.message ?? 'No valid results there is an issue with the fetch');
        }

        if (failedResults.length) {
            logger.error('[FAILED_TRIP_REVIEWS] Some requests failed', { failedResults, socialId });
        }

        reviewsFetchCounter.add(1, {
            source: PlatformKey.TRIPADVISOR,
            status: 'success',
        });

        const allReviews = validResults.flat();

        return allReviews;
    }

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    /**
     * Update Reviews in malou database with new review reply
     */
    async reply({
        review,
        comment,
        headerConfig,
        retryReplyingToOtherReviews = true,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        headerConfig?: PlatformHeaderConfigOptions;
        retryReplyingToOtherReviews?: boolean;
    }): Promise<TripadvisorReply & ({} | { error: any; review: IReview })> {
        const tripadvisorComment = comment as TripadvisorReplyPayload;
        let locationId: number | null = null;
        try {
            assert(review.socialLink, 'Missing socialLink on review');
            locationId = extractBusinessId(review.socialLink);
            await replyToReview(PlatformKey.TRIPADVISOR, { review, comment, locationId }, { headerConfig });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'success',
            });
        } catch (error) {
            logger.warn('[ERROR_REPLY_REVIEW_TRIPADVISOR] Error when replying to review, set comment to RETRY', {
                review,
                comment,
                locationId,
                error,
            });
            return { comment: tripadvisorComment.comment, posted: PostedStatus.RETRY, error, review };
        }
        if (retryReplyingToOtherReviews) {
            await this.retryReplyingSomeReviews(headerConfig);
        }
        return { comment: tripadvisorComment.comment, posted: PostedStatus.PENDING };
    }

    pushReviewComment = ({ socialId, key, comment }) => this.reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    updateComment = async function () {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    retryReplyingSomeReviews = async (headerConfig?: PlatformHeaderConfigOptions, reviewsSampleSize = 10): Promise<void> => {
        const reviews = await this.reviewsRepository.getReviewsWithCommentsToRetryReplying(PlatformKey.TRIPADVISOR);
        if (!reviews.length) {
            return;
        }
        const reviewsSample = sampleSize(reviews, reviewsSampleSize);
        const promises = reviewsSample.map(async (review) => this._updateReviewComment(review, headerConfig)).filter(Boolean);
        const results = await Promise.all(promises);
        if (results.length) {
            logger.info(`[RETRY_REPLYING_REVIEWS_TRIPADVISOR] ${results.length} reviews updated`, { results });
        }
        return;
    };

    private _updateReviewComment = async (review: IReview, headerConfig?: PlatformHeaderConfigOptions): Promise<IReview | undefined> => {
        const commentToRetryPosting = review.comments.find((comment) => comment.posted === PostedStatus.RETRY);
        if (!commentToRetryPosting) {
            return;
        }
        const result = await this.reply({
            review,
            comment: { comment: commentToRetryPosting.text },
            headerConfig,
            retryReplyingToOtherReviews: false,
        });
        const currentRetries = commentToRetryPosting.retries ?? 0;
        const retries = result.posted === PostedStatus.RETRY ? currentRetries + 1 : currentRetries;
        const posted = retries > MAX_TRIPADVISOR_REPLY_RETRIES ? PostedStatus.REJECTED : result.posted;

        if (result.posted === PostedStatus.RETRY && posted === PostedStatus.REJECTED) {
            const { name } = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: review.restaurantId },
                options: { lean: true },
                projection: { name: 1 },
            });
            this._slackService.sendAlert({
                channel: SlackChannel.REVIEWS_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.PLATFORM_PUBLISH_ERROR, {
                        message: 'Could not publish reply to Tripadvisor review',
                        metadata: { socialId: review.socialId },
                    }),
                    endpoint: `restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
                    metadata: {
                        description: `Reply could not be published to Tripadvisor for review with 
                        socialId ${review.socialId}`,
                        restaurantName: name,
                    },
                },
            });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
        }

        return this.reviewsRepository.updateReviewCommentStatus({
            commentId: commentToRetryPosting._id,
            posted,
            retries,
        });
    };

    private async _fetchReviewsWithFallback(socialId: string, offset: number, limit: number): Promise<TripadvisorReview[]> {
        try {
            const result = await this._crawlRestaurantReviewsServerDeterminedSorting(socialId, offset, limit);
            assert(result, 'No result from trip server determined sorting');
            return await this._mapApiResponseToReviews(result);
        } catch (error) {
            logger.warn('[TRIPADVISOR_FALLBACK] First method failed, trying default sorting', { socialId, offset, limit, error });
            try {
                const result = await this._crawlRestaurantReviewsDefaultSorting(socialId, offset, limit);
                assert(result, 'No result from trip default sorting');
                return this._mapApiResponseToReviews(result);
            } catch (fallbackError) {
                logger.error('[TRIPADVISOR_FALLBACK] Both methods failed', { socialId, offset, limit, error, fallbackError });
                throw fallbackError;
            }
        }
    }

    private async _mapApiResponseToReviews(
        apiResponse: TripAdvisorApiResponseServerDeterminedSorting | TripAdvisorApiResponseDefaultSorting
    ): Promise<TripadvisorReview[]> {
        const rawReviews =
            (apiResponse as TripAdvisorApiResponseServerDeterminedSorting)?.data?.ReviewsProxy_getReviewListPageForLocation?.[0]?.reviews ??
            (apiResponse as TripAdvisorApiResponseDefaultSorting)?.data?.locations?.[0]?.reviewListPage?.reviews ??
            [];
        if (!rawReviews?.length) {
            return [];
        }

        const translatedReviews = rawReviews.filter((r) => r.originalLanguage !== r.language);

        const originalTextsAndTitles = {};
        try {
            const textsAndTitles = await this._getOriginalTextsAndTitles(translatedReviews.map((r) => r.id));
            assert(textsAndTitles, 'No texts and titles from trip original texts');
            for (let i = 0; i < translatedReviews.length; i++) {
                originalTextsAndTitles[translatedReviews[i].id] = textsAndTitles[i];
            }
        } catch (error) {
            logger.error('[TRIPADVISOR_REVIEWS_ORIGINAL_TEXTS] Error fetching original texts and titles', { error });
        }

        const mapped = rawReviews.map((r: TripAdvisorRawReview) => ({
            id: r.id,
            title: originalTextsAndTitles[r.id]?.title || r.title,
            text: originalTextsAndTitles[r.id]?.text || r.text,
            date: r.publishedDate,
            rating: r.rating,
            lang: originalTextsAndTitles[r.id]?.title ? r.originalLanguage : r.language,
            endpoint:
                (r as TripAdvisorRawReviewDeterminedSorting)?.reviewDetailPageWrapper?.reviewDetailPageRoute?.url ??
                (r as TripAdvisorRawReviewDefaultSorting)?.url,
            answered: r.mgmtResponse?.id
                ? {
                      date: r.mgmtResponse?.publishedDate,
                      answer: r.mgmtResponse?.text,
                      author: {
                          name: r.mgmtResponse?.username,
                      },
                  }
                : undefined,
            profileName: r.userProfile?.displayName || r.username,
            mediaUrls: r.photos?.map((p) => this._getUrlFromTemplate(p.photo.photoSizeDynamic.urlTemplate)),
        }));
        return mapped;
    }

    /**
     * Crawl TripAdvisor reviews using server determined sorting
     * Should return all possible reviews
     * Returns some reviews translated
     */
    private async _crawlRestaurantReviewsServerDeterminedSorting(
        socialId: string,
        offset: number,
        limit: number = 20
    ): Promise<TripAdvisorApiResponseServerDeterminedSorting | undefined> {
        const body = {
            variables: {
                locationId: Number(socialId),
                filters: [],
                limit: limit,
                offset: offset,
                sortType: null,
                sortBy: 'SERVER_DETERMINED',
            },
            extensions: {
                preRegisteredQueryId: '9365c2244f5b46a6',
            },
        };

        const result = await fetchUntilWorks<TripAdvisorApiResponseServerDeterminedSorting>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;', // Looks mandatory but random value seems to work
                },
                body: body,
            },
            isResponseValid: (res) => !!res?.data?.ReviewsProxy_getReviewListPageForLocation,
            retries: 2,
            crawlerCount: 2,
        });

        logger.info('[TRIPADVISOR_REVIEWS_SERVER_DETERMINED_SORTING] Response received', { socialId, offset, limit });
        return result;
    }

    /**
     * Crawl TripAdvisor reviews using default sorting
     * Some reviews might not be returned with this method, there can wholes in the raquette
     */
    private async _crawlRestaurantReviewsDefaultSorting(
        socialId: string,
        offset: number,
        limit: number = 20
    ): Promise<TripAdvisorApiResponseDefaultSorting | undefined> {
        const body = {
            variables: {
                locationId: Number(socialId),
                offset: offset,
                limit: limit,
                keywordVariant: 'location_keywords_v2_llr_order_30_fr',
                needKeywords: false,
                language: 'fr',
                filters: [
                    {
                        axis: 'SORT',
                        selections: ['mostRecent'],
                    },
                ],
                prefs: {
                    showMT: false,
                    sortBy: 'DATE',
                    sortType: '',
                },
                initialPrefs: {
                    showMT: false,
                    sortBy: 'DATE',
                    sortType: '',
                },
            },
            extensions: {
                preRegisteredQueryId: 'aaff0337570ed0aa',
            },
        };

        const result = await fetchUntilWorks<TripAdvisorApiResponseDefaultSorting>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;', // Looks mandatory but random value seems to work
                },
                body: body,
            },
            isResponseValid: (res) => !!res?.data?.locations?.[0]?.reviewListPage?.reviews,
            retries: 2,
            crawlerCount: 2,
        });

        logger.info('[TRIPADVISOR_REVIEWS_DEFAULT_SORTING] Response received', { socialId, offset, limit });
        return result;
    }

    /**
     * When using determined sorting we might get translated reviews
     * This method is used to get the original text and title of the review
     */
    private async _getOriginalTextsAndTitles(reviewIds: number[]): Promise<{ text: string; title: string }[]> {
        // Use cache to prevent fetching all the time
        const foundInCache = await Promise.all(reviewIds.map((id) => this._cache.get(`tripadvisor-original-text-${id}`)));
        const notFoundInCache = reviewIds.filter((_, i) => foundInCache[i] === null);

        if (!notFoundInCache.length) {
            return foundInCache as { text: string; title: string }[];
        }

        const body = {
            variables: {
                reviewsIds: notFoundInCache,
            },
            extensions: {
                preRegisteredQueryId: '55ab9595a36e6548',
            },
        };

        const result = await fetchUntilWorks<TripAdvisorApiResponseOriginalTexts>({
            params: {
                method: 'POST',
                url: 'https://www.tripadvisor.fr/data/graphql/ids',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: '*/*',
                    'Accept-Language': 'fr-FR,fr;q=0.9',
                    'User-Agent':
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                    Cookie: 'TAUnique=%1%enc%3ZDijZOAIJ38FEIUIUHFE2;',
                },
                body: body,
            },
            isResponseValid: (res) => !!res?.data?.ReviewsProxy_getBestLanguageMatchReviewById,
            retries: 2,
            crawlerCount: 2,
        });
        assert(result, 'No result from trip original texts');
        await Promise.all(
            result.data.ReviewsProxy_getBestLanguageMatchReviewById.map((r, i) =>
                this._cache.set(`tripadvisor-original-text-${reviewIds[i]}`, r.reviews, TimeInSeconds.DAY * 7)
            )
        );

        return foundInCache.map((r, i) => r ?? result.data.ReviewsProxy_getBestLanguageMatchReviewById[i].reviews);
    }

    private _getUrlFromTemplate(template: string): string {
        //example of urlTemplate : "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/30/f7/de/1e/caption.jpg?w={width}&h={height}&s=1"
        return template.replace('{width}', '400').replace('{height}', '400');
    }
}
