import { JobAttributesData } from 'agenda';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import {
    IRestaurant,
    IReview,
    IReviewReplyAutomation,
    ITemplate,
    IUser,
    OverwriteOrAssign,
    ReadPreferenceMode,
    toDbId,
} from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    AUTO_REPLY_GENERATE_MAX_FAILED_ATTEMPTS,
    IntelligentSubjectAutomationRelatedEntity,
    ReviewAnalysisSentiment,
    ReviewReplyAutomationMethod,
    REVIEWS_INTELLIGENT_SUBJECTS_TO_DETECT,
    TemplateTag,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { autoReplyToReviewValidator } from ':helpers/validators/jobs/reviews-jobs.validator';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { GenerateReviewReplyUseCase } from ':modules/ai/use-cases';
import { IntelligentSubjectAutomation } from ':modules/automations/features/intelligent-subjects/entities/intelligent-subject-automation.entity';
import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import TemplatesRepository from ':modules/templates/templates.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

import ReviewReplyAutomationsRepository from './features/review-replies/review-replies.repository';

type AutoReplyJobData = z.infer<typeof autoReplyToReviewValidator>;
type ITemplatePopulatedWithUser = OverwriteOrAssign<ITemplate, { user: IUser }>;

const MAX_DAYS_TO_REPLY = 30;

@singleton()
export class AutoReplyUseCases {
    // fake userId
    private readonly _AUTO_REVIEW_REPLY_USER_ID = '6810e1a9cd85eb62fa1450fd';
    constructor(
        private readonly _reviewReplyAutomationsRepository: ReviewReplyAutomationsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _templatesRepository: TemplatesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _generateReviewReplyUseCase: GenerateReviewReplyUseCase,
        private readonly _intelligentSubjectAutomationsRepository: IntelligentSubjectAutomationsRepository,
        private readonly _reviewsRepository: ReviewsRepository
    ) {}

    async handleReviewAutoReply(
        review: IReview,
        { shouldForceNewReply }: { shouldForceNewReply: boolean } = { shouldForceNewReply: false }
    ): Promise<JobAttributesData | void> {
        logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Handling review auto reply', {
            reviewId: review._id,
            restaurantId: review.restaurantId,
            socialId: review.socialId,
        });
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: review.restaurantId },
            projection: { active: 1, name: 1 },
            options: { lean: true },
        });

        if (!restaurant.active) {
            logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Restaurant is not active for review', {
                reviewId: review._id,
                restaurantId: review.restaurantId,
                socialId: review.socialId,
            });
            return;
        }

        const hasExistingComments = shouldForceNewReply ? false : review.comments?.length;
        if (hasExistingComments || review.archived || this._isReviewTooOldToBeReplied(review)) {
            logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Review is not eligible for auto reply', {
                reviewId: review._id,
                restaurantId: review.restaurantId,
                socialId: review.socialId,
                commentsLength: review.comments?.length,
                archived: review.archived,
                isReviewTooOldToBeReplied: this._isReviewTooOldToBeReplied(review),
            });
            return;
        }

        const automation: IReviewReplyAutomation | null = await this._reviewReplyAutomationsRepository.getReviewReplyAutomation(review);
        if (!automation) {
            logger.info('[AutoReplyUseCases] [handleReviewAutoReply] No automation found for review', {
                reviewId: review._id,
                restaurantId: review.restaurantId,
                socialId: review.socialId,
            });
            return;
        }

        const activeIntelligentSubjectAutomations = await this._getReviewIntelligentSubjectsActiveAutomations(review);
        if (activeIntelligentSubjectAutomations) {
            logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Skip auto reply because of intelligent subjects', {
                reviewId: review._id,
                restaurantId: review.restaurantId,
                socialId: review.socialId,
                intelligentSubjects: review.intelligentSubjects,
            });
            return;
        }

        switch (automation.replyMethod) {
            case ReviewReplyAutomationMethod.AI:
                logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Handle with AI', {
                    review,
                    automation,
                });
                return this._handleAutoReplyWithAi(review, automation, { shouldForceNewReply });
            case ReviewReplyAutomationMethod.TEMPLATES:
                logger.info('[AutoReplyUseCases] [handleReviewAutoReply] Handle with Templates', {
                    review,
                    automation,
                    restaurant,
                });
                return this._handleAutoReplyWithTemplates(review, automation, restaurant);
            default:
                logger.warn('[AutoReplyUseCases] [handleReviewAutoReply] Invalid automation reply method', {
                    reviewId: review._id,
                    restaurantId: review.restaurantId,
                    socialId: review.socialId,
                    automationReplyMethod: automation.replyMethod,
                });
                return;
        }
    }

    private async _handleAutoReplyWithAi(
        review: IReview,
        automation: IReviewReplyAutomation,
        { shouldForceNewReply }: { shouldForceNewReply: boolean } = { shouldForceNewReply: false }
    ): Promise<JobAttributesData | void> {
        let generatedReply: {
            aiInteractionId: string;
            completionText: string;
            completionLang?: string;
        } | null = await this._getReviewGeneratedReply(review);
        if (shouldForceNewReply || !generatedReply) {
            const { aiInteractionId, completionText, completionLang } = await this._generateReviewReplyUseCase.execute({
                reviewId: review._id.toString(),
                userId: this._AUTO_REVIEW_REPLY_USER_ID,
                retryCount: AUTO_REPLY_GENERATE_MAX_FAILED_ATTEMPTS,
            });
            assert(completionText);
            generatedReply = {
                aiInteractionId,
                completionText,
                completionLang,
            };
        }

        if (!automation.shouldValidateAiBeforeSend) {
            return this._createJobToPublishReply({
                reviewId: review._id.toString(),
                replyText: generatedReply.completionText,
                interactionId: toDbId(generatedReply.aiInteractionId),
                replyLang: generatedReply.completionLang,
            });
        }
    }

    private async _handleAutoReplyWithTemplates(
        review: IReview,
        automation: IReviewReplyAutomation,
        restaurant: Pick<IRestaurant, 'active' | 'name'>
    ): Promise<JobAttributesData | void> {
        if (!automation.templateIds?.length) {
            return;
        }
        const templates = await this._templatesRepository.find({
            filter: { _id: { $in: automation.templateIds } },
            options: { lean: true, populate: [{ path: 'user' }] },
        });

        if (!templates.length) {
            logger.warn('[AutoReplyUseCases] [_handleAutoReplyWithTemplates] - No templates found for review', {
                reviewId: review._id,
                restaurantId: review.restaurantId,
            });
            return;
        }
        const template = this._getRandomTemplate(templates);

        const restaurantName = restaurant.name;
        const templateText = this._replaceTemplateTags(template.text, { review, restaurantName, template });
        return this._createJobToPublishReply({
            reviewId: review._id.toString(),
            replyText: templateText,
            templateId: toDbId(template._id),
            replyLang: template.language,
        });
    }

    private async _createJobToPublishReply(jobData: AutoReplyJobData): Promise<JobAttributesData | void> {
        logger.info('[AutoReplyUseCases] [_createJobToPublishReply] Creating job to publish reply', {
            reviewId: jobData.reviewId,
        });

        const jobs = await this._agendaSingleton.jobs({
            name: AgendaJobName.AUTO_REPLY_TO_REVIEW,
            'data.reviewId': jobData.reviewId,
        });
        if (jobs?.length) {
            logger.info('[AutoReplyUseCases] [_createJobToPublishReply] Job already scheduled', {
                reviewId: jobData.reviewId,
            });
            return;
        }
        const date = this._getReviewReplyDate();
        const newJob = await this._agendaSingleton.schedule(date, AgendaJobName.AUTO_REPLY_TO_REVIEW, { ...jobData });
        if (newJob) {
            return newJob.attrs;
        }
    }

    private _isReviewTooOldToBeReplied(review: IReview): boolean {
        const socialAvailableDate = review.socialUpdatedAt || review.socialCreatedAt;
        const maxDelayBetweenReviewCreationAndReceptionToReply = DateTime.local().minus({ days: MAX_DAYS_TO_REPLY });
        return DateTime.fromJSDate(socialAvailableDate) < maxDelayBetweenReviewCreationAndReceptionToReply;
    }

    private async _getReviewGeneratedReply(review: IReview): Promise<{ aiInteractionId: string; completionText: string } | null> {
        const aiInteractions = await this._aiInteractionsRepository.find({
            filter: {
                restaurantId: review.restaurantId,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: review._id,
                type: { $in: [AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS, AiInteractionType.REVIEW_ANSWER_TRANSLATION] },
            },
            options: { lean: true },
        });

        const successfulAiInteraction = aiInteractions
            ?.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
            ?.find((aiInteraction) => aiInteraction.completionText?.length && !aiInteraction.error);

        const loggerText = successfulAiInteraction?.completionText
            ? 'Found successful AI interaction'
            : 'No successful AI interaction found';
        logger.info(`[AutoReplyUseCases] [_getReviewGeneratedReply] ${loggerText}`, {
            reviewId: review._id,
            successfulAiInteraction,
        });

        return successfulAiInteraction?.completionText
            ? { aiInteractionId: successfulAiInteraction._id.toString(), completionText: successfulAiInteraction?.completionText }
            : null;
    }

    private _getRandomTemplate(templates: ITemplatePopulatedWithUser[]): ITemplatePopulatedWithUser {
        const randomIndex = Math.floor(Math.random() * templates.length);
        return templates[randomIndex];
    }

    private _replaceTemplateTags(
        text: string,
        { review, restaurantName, template }: { review: IReview; restaurantName: string; template: ITemplatePopulatedWithUser }
    ): string {
        return text
            ?.replaceAll(TemplateTag.CLIENT_NAME, review.reviewer?.displayName ?? '')
            ?.replaceAll(TemplateTag.BUSINESS_NAME, restaurantName)
            ?.replaceAll(TemplateTag.MY_FIRSTNAME, template.user?.name ?? '');
    }

    private _getReviewReplyDate(): Date {
        const randomDelayInMinutes = Math.floor(Math.random() * 120) + 60;
        return DateTime.now().plus({ minutes: randomDelayInMinutes }).toJSDate();
    }

    private async _getReviewIntelligentSubjectsActiveAutomations(review: IReview): Promise<IntelligentSubjectAutomation[] | null> {
        const isFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
            restaurantId: review.restaurantId.toString(),
            featureName: 'release-reviews-intelligent-subjects',
        });
        if (!isFeatureEnabledForRestaurant) {
            return null;
        }

        const automations = await this._intelligentSubjectAutomationsRepository.getIntelligentSubjectAutomationsBySubjects({
            restaurantId: review.restaurantId.toString(),
            relatedEntities: [IntelligentSubjectAutomationRelatedEntity.REVIEWS],
            subjects: REVIEWS_INTELLIGENT_SUBJECTS_TO_DETECT,
        });
        const activeAutomations = automations.filter((automation) => automation.active);
        if (!activeAutomations.length) {
            return null;
        }

        const dbReview = await this._reviewsRepository.findOneOrFail({
            filter: { _id: review._id },
            projection: { intelligentSubjects: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
        });
        const reviewDetectedIntelligentSubjects = dbReview.intelligentSubjects?.filter(
            (subject) => subject.isDetected && subject.sentiment === ReviewAnalysisSentiment.NEGATIVE
        );
        if (!reviewDetectedIntelligentSubjects?.length) {
            return null;
        }

        const activeAutomationsWithMatchingSubjects = activeAutomations.filter((automation) => {
            return reviewDetectedIntelligentSubjects.some(
                (intelligentSubject) => intelligentSubject.subject === automation.subject.toString()
            );
        });

        if (!activeAutomationsWithMatchingSubjects.length) {
            return null;
        }

        return activeAutomationsWithMatchingSubjects;
    }
}
