import { toString } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { DiagnosticDto } from '@malou-io/package-dto';
import { MalouErrorCode, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersApiFactory } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { RestaurantInsightsForDiagnostic } from ':modules/diagnostics/diagnostic.interfaces';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import {
    MINIMUM_AVERAGE_FOLLOWER_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
    MINIMUM_AVERAGE_LIKE_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
    MINIMUM_AVERAGE_POST_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
} from ':modules/diagnostics/diagnostics.constants';
import { InstagramPageDiscoveryService } from ':modules/diagnostics/services/get-instagram-page/instagram-page-discovery.service';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { PostInsight } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export class UpdateDiagnosticWithInstagramRatingUseCase {
    constructor(
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _instagramPageDiscoveryService: InstagramPageDiscoveryService,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(malouDiagnosticId: string): Promise<DiagnosticDto> {
        const partialDiagnostic = await this._diagnosticsRepository.getDiagnosticById(malouDiagnosticId);

        if (!partialDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NOT_FOUND, {
                message: 'Diagnostic not found',
                metadata: { malouDiagnosticId },
            });
        }

        if (!partialDiagnostic.instagramPage?.name) {
            logger.warn('[MALOUPE] [UPDATE_DIAGNOSTIC_WITH_INSTAGRAM_RATING] No Instagram page found for diagnostic', {
                malouDiagnosticId,
            });
            return partialDiagnostic.toDto();
        }

        try {
            const restaurantsInsights = await this._getRestaurantsInsights(partialDiagnostic.similarRestaurantIds.map(toString));

            if (restaurantsInsights.length === 0) {
                const diagnostic = await this._diagnosticsRepository.updateInstagramRatingDetails(partialDiagnostic.id, {
                    averagePostCountForSimilarRestaurants: MINIMUM_AVERAGE_POST_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
                    averageFollowerCountForSimilarRestaurants: MINIMUM_AVERAGE_FOLLOWER_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
                    averageLikeCountForSimilarRestaurants: MINIMUM_AVERAGE_LIKE_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
                });
                if (!diagnostic) {
                    throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                        message: 'Cannot update diagnostic',
                        metadata: { malouDiagnosticId },
                    });
                }

                return diagnostic.toDto();
            }
            const top50PerCentRestaurantsBasedOnFollowers = restaurantsInsights
                .sort((a, b) => b.followerCount - a.followerCount)
                .slice(0, Math.ceil(restaurantsInsights.length / 2));

            const averageRestaurantsFollowers =
                top50PerCentRestaurantsBasedOnFollowers.reduce((acc, restaurantInsight) => {
                    return acc + restaurantInsight.followerCount;
                }, 0) / top50PerCentRestaurantsBasedOnFollowers.length;

            const averageRestaurantsLikes =
                top50PerCentRestaurantsBasedOnFollowers.reduce((acc, restaurantInsight) => {
                    return acc + restaurantInsight.averageLikeCount;
                }, 0) / top50PerCentRestaurantsBasedOnFollowers.length;

            const averageRestaurantsPostCount =
                top50PerCentRestaurantsBasedOnFollowers.reduce((acc, restaurantInsight) => {
                    return acc + restaurantInsight.postCount;
                }, 0) / top50PerCentRestaurantsBasedOnFollowers.length;

            logger.info('[MALOUPE] [UPDATE_DIAGNOSTIC_WITH_INSTAGRAM_RATING] Calculated Instagram rating details', {
                malouDiagnosticId,
                averageRestaurantsFollowers,
                averageRestaurantsLikes,
                averageRestaurantsPostCount,
            });

            await this._diagnosticsRepository.updateInstagramRatingDetails(partialDiagnostic.id, {
                averagePostCountForSimilarRestaurants: Math.max(
                    averageRestaurantsPostCount,
                    MINIMUM_AVERAGE_POST_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS
                ),
                averageFollowerCountForSimilarRestaurants: Math.max(
                    averageRestaurantsFollowers,
                    MINIMUM_AVERAGE_FOLLOWER_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS
                ),
                averageLikeCountForSimilarRestaurants: Math.max(
                    averageRestaurantsLikes,
                    MINIMUM_AVERAGE_LIKE_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS
                ),
            });

            const updatedDiagnostic = await this._diagnosticsRepository.updateIsDiagnosticComplete(partialDiagnostic.id, true);
            if (!updatedDiagnostic) {
                throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                    message: 'Cannot update diagnostic',
                    metadata: { malouDiagnosticId },
                });
            }

            return updatedDiagnostic.toDto();
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.MALOUPE_ALERTS,
                data: {
                    err: error,
                    message: 'Error while updating diagnostic with Instagram rating',
                    metadata: { malouDiagnosticId },
                },
            });
            throw error;
        }
    }

    private async _getRestaurantsInsights(restaurantIds: string[]): Promise<RestaurantInsightsForDiagnostic[]> {
        const startDate = DateTime.now().minus({ months: 1 }).toJSDate();
        return await Promise.all(
            restaurantIds.map(async (restaurantId) => {
                const postsInsights = await this._getRestaurantPostsInsights(restaurantId, startDate);
                const followerCount = await this._getFollowerCountForRestaurant(restaurantId, startDate);
                if (!postsInsights || postsInsights.length === 0) {
                    logger.error('[MALOUPE] No posts insights found for restaurant', { restaurantId });
                    return {
                        postCount: 0,
                        averageLikeCount: 0,
                        followerCount,
                    };
                }
                const postsInsightsData = postsInsights.map((postInsight) => {
                    return {
                        likes: postInsight.likes ?? 0,
                        comments: postInsight.comments ?? 0,
                    };
                });
                return {
                    postCount: postsInsightsData.length,
                    averageLikeCount: postsInsightsData.reduce((acc, postInsight) => acc + postInsight.likes, 0) / postsInsights.length,
                    followerCount,
                };
            })
        );
    }

    private async _getRestaurantPostsInsights(restaurantId: string, startDate: Date): Promise<PostInsight[] | null> {
        const filters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(PlatformKey.INSTAGRAM, {
            startDate,
            endDate: new Date(),
        });
        return await this._instagramPageDiscoveryService.getRestaurantPostsDetails(restaurantId, filters);
    }

    private async _getFollowerCountForRestaurant(restaurantId: string, startDate: Date): Promise<number> {
        const followersInsights = await this._platformInsightsRepository.getLastMetricOccurrence({
            platformKey: PlatformKey.INSTAGRAM,
            metric: StoredInDBInsightsMetric.FOLLOWERS,
            restaurantId,
            endDate: new Date(),
            startDate,
        });
        return followersInsights?.value ?? 0;
    }
}
