import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
class UpdateShouldNotHaveStoreLocatorPage {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(): Promise<void> {
        const restaurantsIds = [];

        await this._restaurantsRepository.updateMany({
            filter: { _id: { $in: restaurantsIds } },
            update: { $set: { shouldNotHaveStoreLocatorPage: true } },
        });
    }
}

const task = container.resolve(UpdateShouldNotHaveStoreLocatorPage);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
