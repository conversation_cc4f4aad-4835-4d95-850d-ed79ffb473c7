import 'reflect-metadata';

import ':env';

import { container } from 'tsyringe';

import { IReview, ReviewModel } from '@malou-io/package-models';

import { AutoReplyUseCases } from ':modules/automations/auto-reply.use-cases';
import ':plugins/db';

const autoReplyUseCases = container.resolve(AutoReplyUseCases);

async function main() {
    // List of review IDs to answer with AI
    // Replace this array with the actual review IDs you want to process
    const reviewIds: string[] = [
        // id list
        // '689afac7cfd650767c23a1d6',
    ];

    if (!reviewIds.length) {
        console.info('No review IDs provided. Please add review IDs to the reviewIds array.');
        process.exit(1);
    }

    console.info(`About to answer ${reviewIds.length} reviews with AI`);

    // Fetch reviews from database
    const reviews = await ReviewModel.find({ _id: { $in: reviewIds } }, {}, { lean: true });

    if (reviews.length !== reviewIds.length) {
        console.info(`Warning: Found ${reviews.length} reviews out of ${reviewIds.length} requested`);
        const foundIds = reviews.map((r) => r._id.toString());
        const missingIds = reviewIds.filter((id) => !foundIds.includes(id));
        console.info('Missing review IDs:', missingIds);
    }

    // Process reviews in batches
    const batchSize = 10;
    let okCount = 0;
    let koCount = 0;
    let processedCount = 0;

    console.info('Starting to process reviews...');

    for (let i = 0; i < reviews.length; i += batchSize) {
        const batch = reviews.slice(i, i + batchSize);

        console.info(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(reviews.length / batchSize)}`);
        console.info(`Reviews ${i + 1}-${Math.min(i + batchSize, reviews.length)} of ${reviews.length}`);

        const promises = batch.map(async (review: IReview) => {
            try {
                const result = await autoReplyUseCases.handleReviewAutoReply(review);
                return result ? 'success' : 'skipped';
            } catch (error) {
                console.error(`Error processing review ${review._id}:`, error);
                return 'error';
            }
        });

        const results = await Promise.all(promises);

        const batchOkCount = results.filter((r) => r === 'success').length;
        const batchSkippedCount = results.filter((r) => r === 'skipped').length;
        const batchErrorCount = results.filter((r) => r === 'error').length;

        okCount += batchOkCount;
        koCount += batchSkippedCount + batchErrorCount;
        processedCount += batch.length;

        console.info(`Batch completed - Success: ${batchOkCount}, Skipped: ${batchSkippedCount}, Errors: ${batchErrorCount}`);
        console.info(`Total progress: ${processedCount}/${reviews.length}`);
        console.info('---');

        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < reviews.length) {
            await new Promise((resolve) => setTimeout(resolve, 5000));
        }
    }

    console.info('=== FINAL RESULTS ===');
    console.info(`Total reviews processed: ${processedCount}`);
    console.info(`Successfully scheduled for AI reply: ${okCount}`);
    console.info(`Skipped or failed: ${koCount}`);
    console.info('===================');
}

main()
    .then(() => {
        console.info('Task completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Task failed:', error);
        process.exit(1);
    });
