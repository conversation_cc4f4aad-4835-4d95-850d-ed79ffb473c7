import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import {
    StoreLocatorCommonElementIds,
    StoreLocatorLanguage,
    StoreLocatorMapPageElementIds,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

interface OrganizationConfiguration {
    organizationId: string;
    cloudfrontDistributionId: string;
    baseUrl: string;
    fonts: {
        class:
            | 'primary'
            | 'primary-bold'
            | 'secondary'
            | 'secondary-bold'
            | 'tertiary'
            | 'tertiary-bold'
            | 'fourth'
            | 'fourth-bold'
            | 'fifth'
            | 'fifth-bold'
            | 'sixth'
            | 'sixth-bold';
        src: string;
        weight?: '400' | '700' | '900';
        style?: 'normal' | 'italic';
    }[];
    colors: {
        class: 'primary' | 'secondary' | 'tertiary' | 'fourth' | 'fifth' | 'sixth';
        value: string;
    }[];
    languages: {
        primary: StoreLocatorLanguage;
        secondary: StoreLocatorLanguage[];
    };
}
@singleton()
class CreateOrganizationConfigurationTask {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(): Promise<void> {
        // const config = await this._getTailwindConfiguration('67cf1ef531d778287af0d2ef');
        await this._handleOrganizationConfiguration({
            organizationId: '',
            cloudfrontDistributionId: '',
            baseUrl: '',
            fonts: [
                {
                    class: 'primary',
                    src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6737367499d558d5c7541a48/fonts/mulish.woff2',
                    weight: '700',
                },
            ],
            colors: [
                {
                    class: 'primary',
                    value: '#9B222D',
                },
                {
                    class: 'secondary',
                    value: '#F6F3F0',
                },
                {
                    class: 'tertiary',
                    value: '#435635',
                },
                {
                    class: 'fourth',
                    value: '#45251C',
                },
            ],
            languages: {
                primary: StoreLocatorLanguage.EN,
                secondary: [],
            },
        });
    }

    /*
    *   Example base url:
    *   https://restaurants.bioburger.fr
    * 
    *   Example fonts: 
        {
            class: 'primary',
            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/fonts/brandon_blk.woff2',
        },
    *   
    *   Example colors:
    *   {
            class: 'primary',
            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/fonts/brandon_blk.woff2',
        },
    */

    private async _handleOrganizationConfiguration({
        baseUrl,
        cloudfrontDistributionId,
        organizationId,
        fonts,
        colors,
        languages,
    }: OrganizationConfiguration) {
        const storePageStyles = {
            [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                'bg-white',
                'text-black',
                'mt-[110px]',
                'xl:h-[calc(100vh-110px)]',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-primary', 'text-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]: ['bg-secondary', 'text-white', 'rounded-sm'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                'bg-secondary',
                'text-white',
                'hover:bg-white',
                'hover:text-secondary',
                'hover:border-secondary',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                'border-primary',
                'bg-primary',
                'text-white',
                'hover:text-primary',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-white'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-white', 'text-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]: ['rounded-lg'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                'bg-secondary',
                'text-white',
                'border-secondary',
                'hover:bg-transparent',
                'hover:text-secondary',
                'hover:border-secondary',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-secondary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                'bg-transparent',
                'border-white',
                'text-white',
                'hover:bg-white',
                'hover:text-secondary',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-white'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-primary', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-black'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-primary', 'text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-primary', 'text-tertiary'],
        };
        const mapPageStyles = {
            [StoreLocatorMapPageElementIds.STORE_LIST_SEARCH_WRAPPER]: ['bg-primary'],
            [StoreLocatorMapPageElementIds.STORE_LIST_SEARCH_INPUT]: ['bg-secondary', 'border-secondary'],
            [StoreLocatorMapPageElementIds.STORE_LIST_SEARCH_INPUT_ICON]: [],
            [StoreLocatorMapPageElementIds.STORE_LIST_SEARCH_BUTTON]: ['bg-secondary', 'border-secondary'],
            [StoreLocatorMapPageElementIds.STORE_LIST_ITEMS_WRAPPER]: ['bg-fourth'],
            [StoreLocatorMapPageElementIds.STORE_LIST_ITEM]: ['text-tertiary', 'font-primary', 'bg-white', 'border-secondary'],
            [StoreLocatorMapPageElementIds.STORE_LIST_ITEM_TITLE]: ['font-primary'],
            [StoreLocatorMapPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK]: ['bg-secondary', 'text-white'],
            [StoreLocatorMapPageElementIds.MAP_POPUP_WRAPPER]: ['text-tertiary', 'font-primary'],
            [StoreLocatorMapPageElementIds.MAP_POPUP_TITLE]: [],
            [StoreLocatorMapPageElementIds.MAP_AND_STORE_LIST_WRAPPER]: ['mt-20'],
            [StoreLocatorMapPageElementIds.MAP_MARKER_GROUP]: ['bg-primary', 'text-tertiary'],
            [StoreLocatorMapPageElementIds.MAP_PAGE_ICONS]: ['fill-secondary'],
            [StoreLocatorMapPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET]: ['bg-secondary', 'text-primary'],
        };

        const commonStyles = {
            [StoreLocatorCommonElementIds.WHITE_MARK_WRAPPER]: ['font-secondary', 'bg-tertiary', 'text-white'],
            [StoreLocatorCommonElementIds.WHITE_MARK_LOGO]: ['fill-white'],
        };

        await this._storeLocatorOrganizationConfigRepository.create({
            data: {
                organizationId: toDbId(organizationId),
                cloudfrontDistributionId,
                baseUrl,
                languages,
                isLive: false,
                styles: {
                    fonts,
                    colors,
                    pages: {
                        store: storePageStyles,
                        map: mapPageStyles,
                        common: commonStyles,
                        storeDraft: {},
                        mapDraft: {},
                    },
                },
            },
        });
    }

    private async _getTailwindConfiguration(organizationId: string) {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        if (!storeLocatorOrganizationConfig) {
            throw new Error(`No configuration found for organization ${organizationId}`);
        }

        // Assuming GenerateTailwindConfigurationService is implemented and available
        return this._generateTailwindConfigurationService.execute({ storeLocatorOrganizationConfig });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
