import { S3 } from 'aws-sdk';
import axios from 'axios';
import { Readable } from 'stream';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { aws } from ':plugins/aws';
import {
    DistantStorageService,
    DistantStorageServiceSaveOptions,
    ObjectAttributes,
} from ':services/distant-storage-service/distant-storage-service.interface';

/**
 * Objects tagged with the {ExpireIn7DaysAwsS3Tag} will be deleted 7 days after their upload.
 * Currently, to avoid a disaster, this rule only applies to objects in the 'media-folder-for-publication' folder.
 * The rule is configured in AWS S3 under the bucket's Management tags and the Lifecycle configuration.
 */
export const ExpireIn7DaysAwsS3Tag = { key: 'ExpireIn7Days', value: 'true' };

@singleton()
export class AwsS3DistantStorageService implements DistantStorageService {
    private _s3Instance: S3;
    private _bucketName = Config.services.s3.bucketName;
    private _awsRegion = Config.services.aws.region;

    constructor() {
        this._s3Instance = new aws.S3();
    }

    async saveFromBuffer(path: string, buffer: Buffer, options?: DistantStorageServiceSaveOptions): Promise<void> {
        const cleanedPath = this._cleanPath(path);
        await this._s3Instance
            .putObject({ Bucket: this._bucketName, Key: cleanedPath, Body: buffer, ContentType: options?.contentType })
            .promise();
    }

    async saveFromUrl(path: string, url: string, options?: DistantStorageServiceSaveOptions): Promise<void> {
        const response = await axios.get(url, { responseType: 'stream' });

        await this.saveFromReadable(path, response.data, options);
    }

    async saveFromReadable(path: string, readable: Readable, options?: DistantStorageServiceSaveOptions): Promise<void> {
        const cleanedPath = this._cleanPath(path);
        await this._s3Instance
            .upload({ Bucket: this._bucketName, Key: cleanedPath, Body: readable, ContentType: options?.contentType })
            .promise();
    }

    getBuffer(path: string): Promise<Buffer> {
        const cleanedPath = this._cleanPath(path);
        const readStream = this._s3Instance.getObject({ Bucket: this._bucketName, Key: cleanedPath }).createReadStream();

        return new Promise((res, rej) => {
            const chunks = Array<any>();
            readStream.on('data', (chunk) => chunks.push(chunk));
            readStream.on('end', () => res(Buffer.concat(chunks)));
            readStream.on('error', (err) => rej(err));
        });
    }

    getReadable(path: string): Readable {
        const cleanedPath = this._cleanPath(path);
        return this._s3Instance.getObject({ Bucket: this._bucketName, Key: cleanedPath }).createReadStream();
    }

    /**
     * Warning: This function has side effects! It actually changes ACLs to make the
     * object public.
     */
    async getPublicAccessibleUrl(path: string): Promise<string> {
        const cleanedPath = this._cleanPath(path);
        await this._s3Instance.putObjectAcl({ Bucket: this._bucketName, Key: cleanedPath, ACL: 'public-read' }).promise();
        return `https://${this._bucketName}.s3.${this._awsRegion}.amazonaws.com/${cleanedPath}`;
    }

    async delete(path: string): Promise<void> {
        await this._s3Instance.deleteObject({ Bucket: this._bucketName, Key: path }).promise();
    }

    /** Returns the URL of the object using the s3:// protocol. */
    public getPrivateS3Url(objectKey: string): `s3://${string}/${string}` {
        return `s3://${this._bucketName}/${objectKey}`;
    }

    public async getObjectAttributes(key: string): Promise<ObjectAttributes | null> {
        const result = await this._s3Instance
            .getObjectAttributes({ Bucket: this._bucketName, Key: key, ObjectAttributes: ['ObjectSize'] }, undefined)
            .promise();
        return { key, size: result.ObjectSize ?? 0 };
    }

    async putTag(key: string, tags: Record<string, string>): Promise<void> {
        const tagSet = Object.entries(tags).map(([k, v]) => ({ Key: k, Value: v }));
        await this._s3Instance.putObjectTagging({ Bucket: this._bucketName, Key: key, Tagging: { TagSet: tagSet } }, undefined).promise();
    }

    private _cleanPath(path: string): string {
        return path.startsWith('/') ? path.slice(1) : path;
    }
}
