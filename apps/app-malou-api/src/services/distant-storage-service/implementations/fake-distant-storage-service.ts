import assert from 'node:assert/strict';
import { Readable } from 'stream';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { readToEnd } from ':helpers/streams/read-to-end';
import { DistantStorageService, ObjectAttributes } from ':services/distant-storage-service/distant-storage-service.interface';

@singleton()
export class FakeDistantStorageService implements DistantStorageService {
    public readonly objects: Map<string, Buffer> = new Map();

    async saveFromBuffer(path: string, buffer: Buffer): Promise<void> {
        this.objects.set(path, buffer);
    }
    saveFromUrl(_path: string, _url: string): Promise<void> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED);
    }
    async getBuffer(path: string): Promise<Buffer> {
        const o = this.objects.get(path);
        assert(o);
        return o;
    }
    getReadable(path: string): Readable {
        const o = this.objects.get(path);
        assert(o);
        return Readable.from([o]);
    }
    async delete(path: string): Promise<void> {
        this.objects.delete(path);
    }
    async saveFromReadable(key: string, readable: Readable): Promise<void> {
        this.objects.set(key, await readToEnd(readable));
    }
    async getPublicAccessibleUrl(key: string): Promise<string> {
        return `public://${key}`;
    }

    async getObjectAttributes(key: string): Promise<ObjectAttributes> {
        return { key, size: (await this.getBuffer(key)).length };
    }

    async putTag(_key: string, _tags: Record<string, string>): Promise<void> {
        return;
    }

    getPrivateS3Url(key: string): string {
        return `s3://${key}`;
    }
}
