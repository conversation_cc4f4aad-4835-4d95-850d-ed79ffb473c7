import { Readable } from 'stream';

import { MimeType } from '@malou-io/package-utils';

export interface DistantStorageServiceSaveOptions {
    /**
     * It is strongly recommended to set this parameter if the object is supposed to be displayed by a browser.
     * In fact this is the value of the Content-Type HTTP header that will be sent by AWS when a client
     * will request the object later.
     */
    contentType?: MimeType;
}

export type ObjectAttributes = {
    key: string;

    /** size in bytes */
    size: number;
};

export interface DistantStorageService {
    saveFromBuffer(path: string, buffer: Buffer, options?: DistantStorageServiceSaveOptions): Promise<void>;
    saveFromUrl(path: string, url: string, options?: DistantStorageServiceSaveOptions): Promise<void>;
    saveFromReadable(path: string, readable: Readable, options?: DistantStorageServiceSaveOptions): Promise<void>;
    getBuffer(path: string): Promise<Buffer>;
    getReadable(path: string): Readable;
    getPublicAccessibleUrl(path: string): Promise<string>;
    delete(path: string): Promise<void>;
    getObjectAttributes(key: string): Promise<ObjectAttributes | null>;
    putTag(key: string, tags: Record<string, string>): Promise<void>;
}
